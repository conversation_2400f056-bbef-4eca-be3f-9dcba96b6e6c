package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.bq.data.base.bean.BaseEnterpriseUser;
import com.bq.linkcore.bean.entity.TenantDO;
import com.bq.linkcore.bean.entity.UserDO;
import com.bq.linkcore.bean.entity.UserTenantRelDO;
import com.bq.linkcore.bean.entity.UserTokenRelDO;
import com.bq.linkcore.dao.mapper.TenantMapper;
import com.bq.linkcore.dao.mapper.UserMapper;
import com.bq.linkcore.dao.mapper.UserTenantRelMapper;
import com.bq.linkcore.dao.mapper.UserTokenRelMapper;
import com.bq.linkcore.utils.SecretKeyUtil;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName: UserBiz
 * @Description:
 * @author: lmy
 * @date: 2022年05月30日 14:28
 */
@Component
public class UserBiz {
    @Resource
    private UserMapper userMapper;
    @Resource
    private UserTokenRelMapper userTokenRelMapper;
    @Resource
    private TenantMapper tenantMapper;
    @Resource
    private UserTenantRelMapper userTenantRelMapper;

    @Autowired
    private RedissonClient redissonClient;

    public TenantDO queryUserTenant(Long userId) {
        UserTenantRelDO userTenantRelDO = queryUserTenantRel(userId);
        if (userTenantRelDO == null) {
            return null;
        }

        return queryTenant(userTenantRelDO.getTenantCode());
    }

    public UserTenantRelDO queryUserTenantRel(Long userId) {
        Wrapper wrapper = new LambdaQueryWrapper<UserTenantRelDO>()
                .eq(UserTenantRelDO::getUserId, userId);

        return userTenantRelMapper.selectOne(wrapper);
    }

    public TenantDO queryTenant(String tenantCode) {
        Wrapper wrapper = new LambdaQueryWrapper<TenantDO>()
                .eq(TenantDO::getCode, tenantCode);

        return tenantMapper.selectOne(wrapper);
    }

    public UserDO selectUserByPhone(String phone) {
        Wrapper wrapper = new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getIsDel, 0)
                .eq(UserDO::getPhone, phone);

        List<UserDO> userDOList = userMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(userDOList)) {
            return null;
        }

        return userDOList.get(0);
    }

    public UserDO selectUserByEmail(String email) {
        Wrapper wrapper = new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getIsDel, 0)
                .eq(UserDO::getEmail, email);

        List<UserDO> userDOList = userMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(userDOList)) {
            return null;
        }

        return userDOList.get(0);
    }


    public UserDO selectUserByName(String username) {
        Wrapper wrapper = new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getIsDel, 0)
                .eq(UserDO::getUsername, username);

        List<UserDO> userDOList = userMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(userDOList)) {
            return null;
        }

        return userDOList.get(0);
    }

    public List<UserDO> selectAllUser() {
        Wrapper wrapper = new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getIsDel, 0);

        return userMapper.selectList(wrapper);
    }

    public UserDO selectUser(Long userId) {
        Wrapper wrapper = new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getIsDel, 0)
                .eq(UserDO::getId, userId);

        return userMapper.selectOne(wrapper);
    }

    public List<UserDO> selectUsers(Set<Long> userIds) {
        Wrapper wrapper = new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getIsDel, 0)
                .in(UserDO::getId, userIds);

        return userMapper.selectList(wrapper);
    }

    public Map<Long, UserDO> selectUserMap(Set<Long> userIds) {
        Wrapper wrapper = new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getIsDel, 0)
                .in(UserDO::getId, userIds);

        List<UserDO> userDOList = userMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(userDOList)) {
            return new HashMap<>();
        }

        Map<Long, UserDO> userDOMap = userDOList.stream().collect(
                Collectors.toMap(UserDO::getId, t -> t));

        return userDOMap;
    }

    public boolean updateUser(UserDO userDO) {
        return userMapper.updateById(userDO) > 0;
    }

    public UserDO selectUserByAccount(String email, String pwd) {
        Wrapper wrapper = new LambdaQueryWrapper<UserDO>()
                .eq(UserDO::getIsDel, 0)
                .eq(UserDO::getEmail, email)
                .eq(UserDO::getPassword, pwd);

        List<UserDO> userDOList = userMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(userDOList)) {
            return null;
        }

        return userDOList.get(0);
    }

    public UserDO insertUser(String phone, String username, String nickName, String password) {

        UserDO userDO = UserDO.builder()
                .phone(phone)
                .username(username)
                .nickname(nickName)
                .creator(0L)
                .createTime(LocalDateTime.now())
                .updater(0L)
                .updateTime(LocalDateTime.now())
                .build();

        if (StringUtils.isNotEmpty(password)){
            userDO.setPassword(SecretKeyUtil.encodeBase64String(password));
        }

        boolean ret = SqlHelper.retBool(userMapper.insert(userDO));
        if (ret) {
            return userDO;
        }

        return null;
    }

    public UserTokenRelDO selectToken(Long userId) {
        Wrapper wrapper = new LambdaQueryWrapper<UserTokenRelDO>()
                .eq(UserTokenRelDO::getUserId, userId);

        List<UserTokenRelDO> userTokenRelDOList = userTokenRelMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(userTokenRelDOList)) {
            return null;
        }

        return userTokenRelDOList.get(0);
    }

    public boolean insertUserToken(UserTokenRelDO userTokenRelDO) {
        return SqlHelper.retBool(userTokenRelMapper.insert(userTokenRelDO));
    }

    public boolean updateUserToken(UserTokenRelDO userTokenRelDO) {
        UpdateWrapper<UserTokenRelDO> wrapper = new UpdateWrapper<>();

        wrapper.eq("user_id", userTokenRelDO.getUserId());
        wrapper.set("token", userTokenRelDO.getToken());
        wrapper.set("ssotoken", userTokenRelDO.getSsotoken());

        int count = userTokenRelMapper.update(null, wrapper);
        return count > 0;
    }

    public void updateUserCache(BaseEnterpriseUser baseUser, UserDO userDO) {
        BaseEnterpriseUser user = buildBaseUser(userDO, baseUser.getToken(), baseUser.getSsotoken(), "");
        RBucket<BaseEnterpriseUser> bucketLoginUser = redissonClient.getBucket(baseUser.getToken());
        bucketLoginUser.set(user, 15, TimeUnit.DAYS);
    }

    public BaseEnterpriseUser buildBaseUser(UserDO userDO, String token, String ssoToken, String tenantCode) {
        BaseEnterpriseUser baseUser = new BaseEnterpriseUser();
        baseUser.setToken(token);
        baseUser.setSsotoken(ssoToken);
        baseUser.setId(userDO.getId());
        baseUser.setUsername(userDO.getUsername());
        baseUser.setPhone(userDO.getPhone());
        baseUser.setAvatar(userDO.getAvatar());
        baseUser.setNickname(userDO.getNickname());
        baseUser.setTenantCode(tenantCode);
        return baseUser;
    }

    public Map<Long,String> queryUserNameMap(List<Long> userIdList) {
        QueryWrapper<UserDO> queryWrapper = Wrappers.query();
        queryWrapper.select("id", "username")
                .in("id", userIdList)
                .eq("is_del", 0);

        List<Map<String, Object>> results = userMapper.selectMaps(queryWrapper);
        return results.stream()
                .collect(Collectors.toMap(
                        result -> (Long) result.get("id"),
                        result -> (String) result.get("username")
                ));

    }

}
