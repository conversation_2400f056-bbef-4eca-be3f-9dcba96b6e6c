package com.bq.linkcore.client.tikhub;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bq.linkcore.client.tikhub.models.ArticleDetailModel;
import com.bq.linkcore.client.tikhub.models.TikHubXHSArticle;
import com.bq.linkcore.utils.DateTimeUtil;
import com.bq.linkcore.utils.StringUtil;
import com.bq.linkcore.utils.XHSNumberParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/9/29 16:32
 * @className TikHubXhsClient
 * @description
 */
@Slf4j
@Component
public class TikHubXhsClient {
    @Resource(name = "commonRestTemplate")
    private RestTemplate restTemplate;

    public ArticleDetailModel getXhsDetailV2(String workId) {
        String url = "https://api.tikhub.io/api/v1/xiaohongshu/web_v2/fetch_feed_notes?note_id=";
        url += workId;

        JSONObject object = callGet(url);
        if (object == null) {
            return null;
        }

        ArticleDetailModel articleDetailModel = new ArticleDetailModel();
        try {
            JSONObject data = object.getJSONObject("data");
            Integer code = data.getInteger("code");
            if (code != 0) {
                return null;
            }

            JSONArray d = data.getJSONArray("data");
            if (d.size() == 0) {
                return null;
            }

            JSONObject notes = d.getJSONObject(0);

            JSONObject user = notes.getJSONObject("user");
            String author_id = user.getString("userid");
            articleDetailModel.setAuthorId(author_id);
            articleDetailModel.setAuthorIdentity(author_id);
            articleDetailModel.setAuthorName(StringUtil.parseStr(user, "name"));
            articleDetailModel.setAuthorAvatar(StringUtil.parseStr(user, "image"));
            articleDetailModel.setAuthorUrl("https://www.xiaohongshu.com/user/profile/" + author_id);

            JSONArray noteList = notes.getJSONArray("note_list");
            if (CollectionUtils.isEmpty(noteList)) {
                return null;
            }

            JSONObject note = noteList.getJSONObject(0);
            articleDetailModel.setWorkId(workId);
            articleDetailModel.setWorkUuid(workId);
            articleDetailModel.setUrl("https://www.xiaohongshu.com/discovery/item/" + workId);
            articleDetailModel.setTitle(StringUtil.parseStr(note, "title"));
            articleDetailModel.setContent(StringUtil.parseStr(note, "desc"));
            articleDetailModel.setDigest(StringUtil.parseStr(note, "desc"));

            Long ts = note.getLong("time");
            articleDetailModel.setPublishTime(DateTimeUtil.covertDateTime(ts));
            articleDetailModel.setLocationIp(StringUtil.parseStr(note, "ip_location"));

            articleDetailModel.setCollectCount(StringUtil.parseInt(note, "collected_count"));
            articleDetailModel.setLikeCount(StringUtil.parseInt(note, "liked_count"));
            articleDetailModel.setCommentCount(StringUtil.parseInt(note, "comments_count"));
            articleDetailModel.setShareCount(StringUtil.parseInt(note, "shared_count"));

            try {
                JSONArray topics = note.getJSONArray("topics");
                JSONObject topic = topics.getJSONObject(0);
                articleDetailModel.setThumbnailLink(StringUtil.parseStr(topic, "image"));
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

        return articleDetailModel;
    }


    /***
     * 获取搜索笔记
     * https://api.tikhub.io/api/v1/xiaohongshu/web/search_notes
     *
     * @param keyword：搜索关键词
     * @param sortType：排序方式
     *          general：综合
     *          time_descending：最新
     *          popularity_descending：最热
     * @param noteType: 笔记类型
     *          0：全部
     *          1：视频
     *          2：图文
     * @param count: 文章总数
     *
     * @return
     */
    public List<TikHubXHSArticle> searchXhsNotesV1(String keyword,
                                                   String sortType,
                                                   String noteType,
                                                   Integer count) {
        String url = "https://api.tikhub.io/api/v1/xiaohongshu/web/search_notes?";
        Map<String, TikHubXHSArticle> tikHubXHSArticleMap = new HashMap<>();

        Integer pageNo = 1;
        Integer pageCount = 1;
        boolean hasMore = true;

        Integer total = 0;
        while (hasMore || (pageNo > pageCount)) {
            JSONObject object = searchXhsNotePageV1(url, keyword, pageNo, sortType, noteType);
            if (object != null) {
                JSONObject data = object.getJSONObject("data");
                data = data.getJSONObject("data");
                JSONArray items = data.getJSONArray("items");

                for (int index = 0; index < items.size(); index++) {
                    try {
                        JSONObject note = items.getJSONObject(index).getJSONObject("note");
                        String workId = note.getString("id");

                        JSONObject userObject = note.getJSONObject("user");
                        String authorId = userObject.getString("userid");

                        Long ts = Long.valueOf(note.getInteger("timestamp"));
                        TikHubXHSArticle tikHubXHSArticle = TikHubXHSArticle.builder()
                                // 作者相关
                                .authorId(authorId)
                                .authorIdentity(authorId)
                                .authorAvatar(userObject.getString("images"))
                                .authorName(userObject.getString("nickname"))
                                .authorUrl("https://www.xiaohongshu.com/user/profile/" + authorId)
                                // 作品相关
                                .workId(workId)
                                .workUuid(workId)
                                .url("https://www.xiaohongshu.com/discovery/item/" + workId)
                                .downloadUrl("")
                                .longUrl("")
                                .digest(userObject.getString("desc"))
                                .title(userObject.getString("title"))
                                .content(userObject.getString("desc"))
                                // 图片集字段，多个图片DFT的url，用分号隔开
                                .imgUrls("")
                                .videoUrls("") // 无视频url字段
                                .publishTime(DateTimeUtil.covertDateTime(ts))
                                .publishDay(null) // 发布日期没有具体值
                                .locationIp("") // 无地理位置字段
                                .likeCount(note.getInteger("liked_count"))
                                .commentCount(note.getInteger("comments_count"))
                                .shareCount(note.getInteger("shared_count"))
                                .collectCount(note.getInteger("collected_count"))
                                .build();


                        JSONArray imageList = note.getJSONArray("images_list");
                        if (imageList != null && imageList.size() > 0) {
                            Integer imageIndex = note.getInteger("cover_image_index");
                            StringBuilder images = new StringBuilder();
                            for (int i = 0; i < imageList.size(); i++) {
                                if (imageIndex == i) {
                                    tikHubXHSArticle.setThumbnailLink(imageList.getString(i));
                                }
                                images.append(imageList.getString(i));
                                images.append(";");
                            }

                            tikHubXHSArticle.setImgUrls(images.toString());
                        }


                        JSONObject geoInfo = note.getJSONObject("geo_info");
                        if (geoInfo != null) {
                            try {
                                String location = geoInfo.getString("distance");
                                System.out.println("location=" + location);
                                tikHubXHSArticle.setLocationIp(location);
                            } catch (Exception e) {
                                log.error(e.getMessage(), e);
                            }
                        }

                        tikHubXHSArticleMap.put(tikHubXHSArticle.getWorkId(), tikHubXHSArticle);
                    } catch (Exception e) {
                        log.error("searchXhsNotesV1 error={} e={}", e.getMessage(), e);
                    }
                }


                if (CollectionUtils.isEmpty(items) || total >= count) {
                    hasMore = false;
                }

                pageNo = pageNo + 1;
            } else {
                hasMore = false;
            }
        }

        return new ArrayList<>(tikHubXHSArticleMap.values());
    }

    public JSONObject searchXhsNotePageV1(String url,
                                          String key,
                                          Integer pageNo,
                                          String sortType,
                                          String noteType) {
        try {
            url = url + "page=" + pageNo + "&sort=" + sortType +
                    "&noteType=" + noteType + "&keyword=" + key;

            JSONObject data = callGet(url);
            if (data == null) {
                data = callGet(url);
            }

            if (data == null) {
                return null;
            }

            Integer code = data.getInteger("code");
            if (!code.equals(200)) {
                return null;
            }

            return data;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }


    public JSONObject searchXhsNotePageV2(String url,
                                          String key,
                                          Integer pageNo,
                                          String sortType,
                                          String noteType,
                                          String noteTime) {
        try {
            url = url + "&page=" + pageNo + "&sort_type=" + sortType +
                    "&note_type=" + noteType + "&keywords=" + URLEncoder.encode(key, "UTF-8");

            JSONObject data = callGet(url);
            if (data == null) {
                data = callGet(url);
            }

            return data;
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    /***
     * 获取搜索笔记
     * https://api.tikhub.io/api/v1/xiaohongshu/web_v2/fetch_search_notes
     *
     * @param keyword: 搜索关键词
     * @param sortType: 排序方式
     *      综合排序（默认参数）: general
     *      最热排序: popularity_descending
     *      最新排序: time_descending
     *      最多评论: comment_descending
     *      最多收藏: collect_descending
     * @param noteType: 笔记类型
     *      综合笔记（默认参数）: _0
     *      视频笔记: _1
     *      图文笔记: _2
     *      直播: _3
     * @param noteTime: 发布时间
     *      不限: ""
     *      一天内 :一天内
     *      一周内 :一周内
     *      半年内 :半年内
     * @param count: 文章总数
     *
     * @return
     */
    public List<TikHubXHSArticle> searchXhsNotesV2(String keyword,
                                                   String sortType,
                                                   String noteType,
                                                   String noteTime,
                                                   Integer count) {
        String url = "https://api.tikhub.io/api/v1/xiaohongshu/web_v2/fetch_search_notes?";
        Map<String, TikHubXHSArticle> tikHubXHSArticleMap = new HashMap<>();

        Integer pageNo = 1;
        Integer pageCount = 1;
        boolean hasMore = true;

        Integer total = 0;
        while (hasMore || (pageNo > pageCount)) {
            JSONObject object = searchXhsNotePageV2(url, keyword, pageNo, sortType, noteType, noteTime);
            if (object == null) {
                object = searchXhsNotePageV2(url, keyword, pageNo, sortType, noteType, noteTime);
            }
            if (object != null) {
                JSONObject data = object.getJSONObject("data");
                if (!data.containsKey("data")) {
                    break;
                }

                data = data.getJSONObject("data");
                JSONArray items = data.getJSONArray("items");
                if (items == null) {
                    break;
                }

                total += items.size();
                for (int index = 0; index < items.size(); index++) {
                    JSONObject jsonObject = items.getJSONObject(index);
                    JSONObject cardObject = jsonObject.getJSONObject("note");

                    String aId = cardObject.getString("id");

                    JSONObject userObject = cardObject.getJSONObject("user");
                    String authorId = userObject.getString("userid");
                    LocalDateTime ts = null;
                    try {
                        Long timestamp = cardObject.getLong("timestamp") * 1000;
                        ts = DateTimeUtil.covertDateTime(timestamp);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }

                    TikHubXHSArticle tikHubXHSArticle = TikHubXHSArticle.builder()
                            .authorId(authorId)
                            .authorIdentity(authorId)
                            .authorAvatar(userObject.getString("images"))
                            .authorName(userObject.getString("nickname"))
                            .authorUrl("https://www.xiaohongshu.com/user/profile/" + authorId)
                            .workId(aId)
                            .workUuid(aId)
                            .url("https://www.xiaohongshu.com/discovery/item/" + aId)
                            .downloadUrl("")
                            .longUrl("")
                            .digest("")
                            .title(cardObject.getString("display_title"))
                            .content("")
                            .imgUrls("")
                            .videoUrls("")
                            .publishDay(null)
                            .locationIp("")
                            .publishTime(ts)
                            .build();
                    try {
                        tikHubXHSArticle.setLikeCount(XHSNumberParser.parseNumber(cardObject.getString("liked_count")));
                        tikHubXHSArticle.setCommentCount(XHSNumberParser.parseNumber(cardObject.getString("comments_count")));
                        tikHubXHSArticle.setShareCount(XHSNumberParser.parseNumber(cardObject.getString("shared_count")));
                        tikHubXHSArticle.setCollectCount(XHSNumberParser.parseNumber(cardObject.getString("collected_count")));
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }

                    tikHubXHSArticleMap.put(tikHubXHSArticle.getWorkId(), tikHubXHSArticle);
                }

                hasMore = object.getBoolean("has_more");
                if (CollectionUtils.isEmpty(items) || total >= count) {
                    hasMore = false;
                }

                pageNo = pageNo + 1;

            } else {
                hasMore = false;
            }
        }

        return new ArrayList<>(tikHubXHSArticleMap.values());
    }


    /**
     * 通过网关调用服务
     *
     * @param url
     * @param reqJson
     * @return
     */
    public String callPost(String url, String reqJson) {
        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());

        HttpEntity formEntity = new HttpEntity(reqJson, headers);

        try {
            String response = restTemplate.postForEntity(url, formEntity, String.class).getBody();

            log.info("OneBoundHttpClient response={}", response);

            JSONObject object = JSON.parseObject(response);
            Integer code = object.getInteger("code");
            String data = object.getString("data");

            if (code != 0) {
                return null;
            }

            return data;
        } catch (Exception e) {
            log.error("OneBoundHttpClient 调用AutoPaas服务接口出错:", e);
        }

        return null;
    }

    /**
     * 通过网关调用服务
     *
     * @param url
     * @return
     */
    public JSONObject callGet(String url) {
        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
        headers.setAccept(Collections.singletonList(type));
        headers.setBearerAuth("6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==");
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36");

        try {
            // 创建 HttpEntity 对象，包含请求头和请求体（如果需要）
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送 GET 请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            String body = response.getBody();
            log.info("TikHubXhsClient response={}", response);

            JSONObject object = JSON.parseObject(body);
            Integer code = object.getInteger("code");
            if (!code.equals(200)) {
                log.error("TikHubXhsClient 查询{}服务接口出错:", url);
                return null;
            }

            return object;
        } catch (Exception e) {
            log.error("TikHubXhsClient 调用AutoPaas服务接口出错:", e);
        }

        return null;
    }

    private String convertCity(String key) {
        try {
            if (StringUtils.isBlank(key)) {
                return "";
            }

            if (StringUtil.containsChinese(key)) {
                return key;
            }

            return StringUtil.convertCity(key);
        } catch (Exception e) {
            return "";
        }
    }


}
