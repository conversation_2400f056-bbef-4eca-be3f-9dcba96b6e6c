<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.AtTiktokAuthorProfileRecordMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.AtTiktokAuthorProfileHistoryDO">
        <id column="id" property="id" />
        <result column="author_id" property="authorId" />
        <result column="unique_id" property="uniqueId" />
        <result column="author_name" property="authorName" />
        <result column="author_avatar" property="authorAvatar" />
        <result column="sec_uid" property="secUid" />
        <result column="author_url" property="authorUrl" />
        <result column="desc" property="desc" />
        <result column="register_time" property="registerTime" />
        <result column="is_verified" property="isVerified" />
        <result column="region" property="region" />
        <result column="language" property="language" />
        <result column="private_account" property="privateAccount" />
        <result column="commerce_user" property="commerceUser" />
        <result column="tt_seller" property="ttSeller" />
        <result column="commerce_category" property="commerceCategory" />
        <result column="follower_count" property="followerCount" />
        <result column="following_count" property="followingCount" />
        <result column="heart_count" property="heartCount" />
        <result column="video_count" property="videoCount" />
        <result column="friend_count" property="friendCount" />
        <result column="refresh_time" property="refreshTime" />
        <result column="record_day" property="recordDay" />
        <result column="creator" property="creator" />
        <result column="create_time" property="createTime" />
        <result column="updater" property="updater" />
        <result column="update_time" property="updateTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        id, author_id, unique_id, author_name, author_avatar, sec_uid, author_url, desc, register_time, is_verified, region, language, private_account, commerce_user, tt_seller, commerce_category, follower_count, following_count, heart_count, video_count, friend_count, refresh_time, record_day, creator, create_time, updater, update_time, is_del
    </sql>

  

  
</mapper>
