package com.bq.linkcore.client.tikhub.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthorInfo {

    /**
     * 发布账号ID
     */
    private String authorId;

    private String uniqueId;

    /**
     * 作者昵称
     */
    private String authorName;

    /**
     * 作者头像
     */
    private String authorAvatar;

    /**
     * 作者账号加密ID-可用于拼接主页
     * 对应secUid
     */
    private String secUid;

    /**
     * 发布者主页地址
     */
    private String authorUrl;

    private String desc;

    /**
     * 创建时间
     */
    private Integer registerTime;

    /**
     * 徽章	        样式	    含义
     * Verified	    蓝色✔️	官方认证账号（真实性）
     * PRO	        金色⭐	    创作者基金资格（非认证）
     * Top Fans	    红色❤️	互动活跃粉丝（无认证意义）
     *
     * 是否认证（false表示无蓝V认证）
     */
    private Integer isVerified;

    /**
     * 用户注册地区（如 "US"表示美国）
     */
    private String region;

    /**
     * 用户界面语言（如 "en"表示英语）
     */
    private String language;

    /**
     * 是否为私密账号（false表示公开）
     */
    private Integer privateAccount;


    /**
     * 是否开通电商功能（false表示未开通）
     */
    private Integer commerceUser = 0;

    /**
     * 开通电商的类目
     */
    private String category = "";


    /**
     * 是否TikTok小店卖家（false表示否）
     */
    private Integer ttSeller;

    /**
     * 粉丝数
     */
    private Integer followerCount;

    /**
     * 关注数
     */
    private Integer followingCount;

    /**
     * 总获赞数
     */
    private Integer heartCount;

    /**
     * 发布视频数
     */
    private Integer videoCount;

    /**
     * 好友数
     */
    private Integer friendCount;

}
