package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorProfileRecordDO;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorProfileRecordMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AtTtAuthorProfileBiz {

    @Resource
    private AtTiktokAuthorProfileRecordMapper tiktokAuthorProfileRecordMapper;

    /**
     * 插入作者主页记录
     * @param authorProfileRecordDO 作者主页记录实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorProfileRecord(AtTiktokAuthorProfileRecordDO authorProfileRecordDO) {
        return tiktokAuthorProfileRecordMapper.insert(authorProfileRecordDO);
    }

    /**
     * 根据作者ID查询作者主页记录
     * @param authorId 作者唯一ID
     * @return 作者主页记录实体对象，不存在则返回null
     */
    public AtTiktokAuthorProfileRecordDO queryAuthorProfileRecordByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO>()
                .eq(AtTiktokAuthorProfileRecordDO::getAuthorId, authorId)
                .eq(AtTiktokAuthorProfileRecordDO::getIsDel, 0);
        return tiktokAuthorProfileRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 根据作者ID查询记录是否存在
     * @param authorId 作者唯一ID
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorProfileRecordExistsByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO>()
                .eq(AtTiktokAuthorProfileRecordDO::getAuthorId, authorId)
                .eq(AtTiktokAuthorProfileRecordDO::getIsDel, 0);
        return tiktokAuthorProfileRecordMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新作者主页记录
     * @param authorProfileRecordDO 作者主页记录实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorProfileRecord(AtTiktokAuthorProfileRecordDO authorProfileRecordDO) {
        return tiktokAuthorProfileRecordMapper.updateById(authorProfileRecordDO);
    }

    /**
     * 根据ID查询作者主页记录
     * @param id 主键ID
     * @return 作者主页记录实体对象，不存在则返回null
     */
    public AtTiktokAuthorProfileRecordDO queryAuthorProfileRecordById(Long id) {
        return tiktokAuthorProfileRecordMapper.selectById(id);
    }

    /**
     * 查询所有未删除的作者主页记录
     * @return 作者主页记录列表
     */
    public List<AtTiktokAuthorProfileRecordDO> queryAllAuthorProfileRecords() {
        LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO>()
                .eq(AtTiktokAuthorProfileRecordDO::getIsDel, 0);
        return tiktokAuthorProfileRecordMapper.selectList(queryWrapper);
    }

    /**
     * 根据作者名称模糊查询作者主页记录
     * @param name 作者名称
     * @return 作者主页记录列表
     */
    public List<AtTiktokAuthorProfileRecordDO> queryAuthorProfileRecordsByName(String name) {
        LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO>()
                .like(AtTiktokAuthorProfileRecordDO::getName, name)
                .eq(AtTiktokAuthorProfileRecordDO::getIsDel, 0);
        return tiktokAuthorProfileRecordMapper.selectList(queryWrapper);
    }
}
