package com.bq.linkcore.controller;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.entity.UserDO;
import com.bq.linkcore.bean.vo.UserLoginReqVo;
import com.bq.linkcore.bean.vo.UserMessageReqVo;
import com.bq.linkcore.common.*;
import com.bq.linkcore.config.EncryptConfig;
import com.bq.linkcore.config.LoginRepeatLimitConfig;
import com.bq.linkcore.services.IUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.regex.Pattern;

import static com.bq.linkcore.common.ResponseMsg.ERROR_INVALID_MESSAGE;
import static com.bq.linkcore.common.ResponseMsg.ERROR_INVALID_PHONE;

@Api(value = "用户", tags = "用户接口")
@RestController
@Slf4j
@RequestMapping("/user")
public class UserController {

    @Autowired
    protected EncryptConfig encryptConfig;

    @Autowired
    private IUserService userService;
    @Resource
    private LoginRepeatLimitConfig limitConfig;


    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "返回消息类", response = ResponseData.class)
    })
    @PostMapping(value = "/message")
    public ResponseData getMessageCode(@Valid @RequestBody UserMessageReqVo vo) {
        try {
            log.info("/user/message, vo={}", vo);

            // 检查邮箱格式
            if (!Pattern.matches(Constants.REG_EMAIL, vo.getEmail())) {
                return RD.fail(ERROR_INVALID_PHONE.getCode());
            }

            return userService.sendMessageByEmail(vo);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return RD.fail(ResponseMsg.ERROR_PHONE_MESSAGE.getCode());
    }


    @ApiOperation(value = "用户登录", notes = "密码使用密文传输")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回信息", response = ResponseData.class)})
    @PostMapping(value = "/login")
    public ResponseData login(@Valid @RequestBody UserLoginReqVo vo) {
        try {
            log.info("/user/login, vo={}", vo);

            // 检查手机号码格式
//            if (!ParamsVerifyUtil.checkPhone(vo.getPhone())) {
//                log.error("login 手机号码格式错误, phone={}", vo.getPhone());
//                return RD.fail(ResponseMsg.ERROR_NO_ACCOUNT.getCode());
//            }

            // 校验失败次数
            boolean limit = userService.checkPwdRepeatLimit(vo.getEmail());
            if (limit) {
                String msg = String.format(ResponseMsg.ERROR_LOGIN_ERR_PWD_TIMES.getMsg(), limitConfig.getPwdRepeatLimit(), limitConfig.getPwdTimeout());
                return ResponseData.fail(ResponseMsg.ERROR_LOGIN_ERR_PWD_TIMES.getCode(), msg);
            }


            // 屏蔽注册登陆接口
            if (vo.getLoginType().equals(LoginTypeEnum.EMAIL.getCode())) {

                boolean ret = userService.checkMessage(vo.getEmail(), vo.getType(), vo.getMessageCode());
                if (!ret) {
                    return RD.fail(ERROR_INVALID_MESSAGE.getCode());
                }

                // 判断是否存在
                UserDO userDO = userService.queryTxUserByEmail(vo.getEmail());
                if (userDO != null) {
                    return userService.loginTxByPhone(vo, userDO);
                } else {
                    return userService.insertTxUser(vo);
                }
            } else {
                return userService.accountLogin(vo);
            }

        } catch (ServiceException e) {
            log.error(e.getMessage(), e);
            return RD.fail(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RD.fail();
        }
    }

}
