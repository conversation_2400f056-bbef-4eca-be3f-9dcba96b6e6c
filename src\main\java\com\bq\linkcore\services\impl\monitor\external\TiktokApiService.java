package com.bq.linkcore.services.impl.monitor.external;

import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * TikTok第三方API服务
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
public class TiktokApiService {

    @Resource
    private TikHubTiktokAccountRequester tikHubTiktokAccountRequester;

    /**
     * 获取用户基本信息
     */
    public TiktokUserProfile getUserProfile(String uniqueId) {
        log.debug("调用TikTok API获取用户信息: {}", uniqueId);
        
        try {
            // 调用真实的TikHub API
            AuthorInfo authorInfo = tikHubTiktokAccountRequester.getUserProfileWebV1(uniqueId, null);
            
            if (authorInfo != null) {
                return convertToTiktokUserProfile(authorInfo);
            }
            
            // 如果真实API没有数据，返回mock数据
            return generateMockUserProfile(uniqueId);
            
        } catch (Exception e) {
            log.error("获取TikTok用户信息异常，uniqueId: {}", uniqueId, e);
            // 异常情况下返回mock数据
            return generateMockUserProfile(uniqueId);
        }
    }

    /**
     * 获取用户作品列表
     */
    public List<TiktokUserWork> getUserWorks(String uniqueId) {
        log.debug("调用TikTok API获取用户作品: {}", uniqueId);
        
        try {
            // TODO: 实现真实的TikTok作品API调用
            // 目前返回mock数据
            return generateMockUserWorks(uniqueId);
            
        } catch (Exception e) {
            log.error("获取TikTok用户作品异常，uniqueId: {}", uniqueId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取作品详细信息
     */
    public TiktokWorkDetail getWorkDetail(String workId) {
        log.debug("调用TikTok API获取作品详情: {}", workId);
        
        try {
            // TODO: 实现真实的TikTok作品详情API调用
            return generateMockWorkDetail(workId);
            
        } catch (Exception e) {
            log.error("获取TikTok作品详情异常，workId: {}", workId, e);
            return null;
        }
    }

    /**
     * 转换AuthorInfo到TiktokUserProfile
     */
    private TiktokUserProfile convertToTiktokUserProfile(AuthorInfo authorInfo) {
        return TiktokUserProfile.builder()
                .uniqueId(authorInfo.getUniqueId())
                .authorId(authorInfo.getAuthorId())
                .authorName(authorInfo.getAuthorName())
                .authorAvatar(authorInfo.getAuthorAvatar())
                .secUid(authorInfo.getSecUid())
                .authorUrl(authorInfo.getAuthorUrl())
                .desc(authorInfo.getDesc())
                .followerCount(authorInfo.getFollowerCount())
                .followingCount(authorInfo.getFollowingCount())
                .heartCount(authorInfo.getHeartCount())
                .videoCount(authorInfo.getVideoCount())
                .friendCount(authorInfo.getFriendCount())
                .isVerified(authorInfo.getIsVerified())
                .region(authorInfo.getRegion())
                .language(authorInfo.getLanguage())
                .createTime(LocalDateTime.now())
                .build();
    }

    /**
     * 生成mock用户信息
     */
    private TiktokUserProfile generateMockUserProfile(String uniqueId) {
        Random random = new Random();
        
        return TiktokUserProfile.builder()
                .uniqueId(uniqueId)
                .authorId("author_" + uniqueId)
                .authorName("TikTok用户_" + uniqueId)
                .authorAvatar("https://tiktok.avatar.com/" + uniqueId + ".jpg")
                .secUid("sec_" + uniqueId + "_" + System.currentTimeMillis())
                .authorUrl("https://tiktok.com/@" + uniqueId)
                .desc("这是用户 " + uniqueId + " 的个人简介")
                .followerCount(1000 + random.nextInt(100000))
                .followingCount(100 + random.nextInt(1000))
                .heartCount(5000 + random.nextInt(500000))
                .videoCount(50 + random.nextInt(500))
                .friendCount(10 + random.nextInt(100))
                .isVerified(random.nextInt(2))
                .region("US")
                .language("en")
                .createTime(LocalDateTime.now())
                .build();
    }

    /**
     * 生成mock用户作品列表
     */
    private List<TiktokUserWork> generateMockUserWorks(String uniqueId) {
        List<TiktokUserWork> works = new ArrayList<>();
        Random random = new Random();
        
        int count = 5 + random.nextInt(10); // 5-15个作品
        
        for (int i = 1; i <= count; i++) {
            TiktokUserWork work = TiktokUserWork.builder()
                    .workId("tiktok_work_" + uniqueId + "_" + i + "_" + System.currentTimeMillis())
                    .uniqueId(uniqueId)
                    .title("TikTok作品标题 " + i)
                    .content("这是第" + i + "个TikTok作品的内容描述")
                    .coverUrl("https://tiktok.cover.com/" + i + ".jpg")
                    .videoUrl("https://tiktok.video.com/" + i + ".mp4")
                    .duration(15 + random.nextInt(180)) // 15-195秒
                    .playCount(1000 + random.nextInt(100000))
                    .likeCount(50 + random.nextInt(5000))
                    .commentCount(5 + random.nextInt(500))
                    .shareCount(2 + random.nextInt(100))
                    .publishTime(LocalDateTime.now().minusDays(random.nextInt(30)))
                    .build();
            
            works.add(work);
        }
        
        log.debug("生成TikTok mock作品数据 {} 条，uniqueId: {}", works.size(), uniqueId);
        return works;
    }

    /**
     * 生成mock作品详情
     */
    private TiktokWorkDetail generateMockWorkDetail(String workId) {
        Random random = new Random();
        
        return TiktokWorkDetail.builder()
                .workId(workId)
                .title("TikTok作品详情标题")
                .content("详细的作品内容描述")
                .tags(List.of("funny", "life", "entertainment"))
                .location("New York, USA")
                .musicId("music_" + System.currentTimeMillis())
                .musicTitle("Background Music Title")
                .playCount(10000 + random.nextInt(1000000))
                .likeCount(500 + random.nextInt(50000))
                .commentCount(50 + random.nextInt(5000))
                .shareCount(10 + random.nextInt(1000))
                .collectCount(5 + random.nextInt(500))
                .createTime(LocalDateTime.now())
                .build();
    }

    /**
     * TikTok用户信息模型
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class TiktokUserProfile {
        private String uniqueId;
        private String authorId;
        private String authorName;
        private String authorAvatar;
        private String secUid;
        private String authorUrl;
        private String desc;
        private Integer followerCount;
        private Integer followingCount;
        private Integer heartCount;
        private Integer videoCount;
        private Integer friendCount;
        private Integer isVerified;
        private String region;
        private String language;
        private LocalDateTime createTime;
    }

    /**
     * TikTok用户作品模型
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class TiktokUserWork {
        private String workId;
        private String uniqueId;
        private String title;
        private String content;
        private String coverUrl;
        private String videoUrl;
        private Integer duration;
        private Integer playCount;
        private Integer likeCount;
        private Integer commentCount;
        private Integer shareCount;
        private LocalDateTime publishTime;
    }

    /**
     * TikTok作品详情模型
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class TiktokWorkDetail {
        private String workId;
        private String title;
        private String content;
        private List<String> tags;
        private String location;
        private String musicId;
        private String musicTitle;
        private Integer playCount;
        private Integer likeCount;
        private Integer commentCount;
        private Integer shareCount;
        private Integer collectCount;
        private LocalDateTime createTime;
    }
}
