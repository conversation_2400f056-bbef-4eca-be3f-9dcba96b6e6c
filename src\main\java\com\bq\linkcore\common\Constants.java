package com.bq.linkcore.common;

/**
 * @ClassName: MyConstants
 * @Description:
 * @author: lmy
 * @date: 2021年8月6日 20:59:03
 */
public class Constants {

    /**
     * 注册时候，redis前缀
     */
    public final static String REGISTER_PHONE_KEY_ = "data_core_register_phone_key_";

    /**
     * 登录时候，redis前缀
     */
    public final static String LOGIN_PHONE_KEY_ = "data_core_login_phone_key_";

    public final static String LOGIN_EMAIL_KEY_ = "data_core_login_phone_key_";

    public final static String USER_LOGIN_ACCOUNT_KEY_ = "data_core_user_login_account_key_";
    public final static String UPDATE_PASSWORD_PHONE_KEY_ = "data_core_update_password_phone_key_";

    /**
     * redis短信验证码，有效时常5分钟
     */
    public final static int MESSAGE_CODE_SIX_OVER_TIME_MINUTES = 5;

    /**
     * redis登录，记住密码自动登录有效期15天
     */
    public final static int AUTO_LOGIN_FIFTEEN_OVER_TIME_DAYS = 15;
    /**
     * oss免密登录，redis前缀
     */
    public final static String SSO_LOGIN_PHONE_KEY_ = "sso_login_phone_key_";
    public final static String SSO_ENTERPRISE_USER_LOGIN_KEY_ = "sso_enterprise_user_login_key_";

    /**
     * 【A-Z a-z 0-9】
     * 【_】【~】【!】【@】【#】【$】【%】【^】【&】【*】【=】【[】【]】【{】【}】【?】【>】【<】【-】【+】【`】【(】【)】【|】【,】【.】【;】【:】
     * 6-15位
     * 除了【斜杠，反斜杠，单引号，双引号】
     */
    public final static String PASSWORD_UNICODE_REGEX = "[^a-zA-Z\\d~\\!@#$%\\^\\&\\*_\\-+='|\\(){}\\[\\]:;\"'<>,.?]";
    public final static String PASSWORD_TYPE_REGEX = "^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_]+$)(?![a-z0-9]+$)(?![a-z\\W_]+$)(?![0-9\\W_]+$)[a-zA-Z0-9\\W_]{8,16}$";

    public final static String REG_PASSWORD = "^[A-Za-z0-9_~!@#$%^&*=\\/\\[`\\|\\,\\.\\(\\);:\\]\\{\\}\\?\\>\\<\\-\\+]+(\\s+[A-Za-z0-9_~!@#$%^&*=\\/\\[`\\|\\,\\.\\(\\);:\\]\\{\\}\\?\\>\\<\\-\\+]+)*${6,16}";
    public final static String REG_PHONE = "^(1[3-9])\\d{9}$";
    public final static String REG_EMAIL = "^([a-z0-9A-Z]+[-|\\._]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";

    /**
     * 发送邮件或短信 key
     */
    public final static String USER_SEND_PHONE_KEY_ = "user_send_phone_key_";

    public final static String USER_SEND_EMAIL_KEY_ = "user_send_email_key_";

    public final static String USER_SEND_EMAIL_LIMIT_KEY_ = "user_send_email_limit_key_";

    public final static String USER_SEND_PHONE_LIMIT_KEY_ = "user_send_phone_limit_key_";

    public final static String USER_CHECK_MESSAGE_CODE_NUM_ = "user_check_message_code_num_";

    public final static String USER_CHECK_PWD_NUM_ = "user_check_pwd_num_";

    public final static String USER_MEMBER_INFO_ = "user_member_info_";

    /**
     * 图形验证码
     */
    public final static String USER_CHAR_CAPTCHA_KEY_ = "user_char_captcha_key_";

    public final static String SUCCESS = "success";

    // 抖音达人标签
    public final static String DOUYIN_AUTHOR_LABEL_INDEX = "douyin_author_label_index";

    public final static String DOUYIN_PRODUCT_LABEL_INDEX = "douyin_product_label_index";

    // 同一时间导出限制
    public final static String EXPORT_AUTHOR_USER_LIST_COUNT = "export_author_user_list_count";

    public final static String DOU_YIN_AUTHOR_LABEL_MAP = "dou_yin_author_label_map";


    public final static String REDIS_KEY_TASK_MONITOR_DATA = "redis_key_task_monitor_data_";


    // 抖音指令 accessToken缓存
    public static final String DOUYIN_ACCESS_TOKEN = "douyin_access_token_";

    // 抖音指令 refreshToken缓存
    public static final String DOUYIN_REFRESH_TOKEN = "douyin_refresh_token_";

    // 抖音直播监控已经完成上传的弹幕缓存
    public static final String DOUYIN_LIVE_MONITOR_BARRAGE_CACHE = "douyin_live_monitor_barrage_cache_";

    // 抖音直播监控已经完成上传的观众缓存
    public static final String DOUYIN_LIVE_MONITOR_AUDIENCE_CACHE = "douyin_live_monitor_audience_cache_";

    public static final Integer DOUYIN_LIVE_MONITOR_TMP_DATA_MAX = 400;

    public static final String DOUYIN_THIRD_PART_AUTH_REFRESH = "douyin_third_part_auth_refresh_";


    // 默认权限
    public static final String role_super_admin = "role_super_admin";

    public static final String role_normal_member = "role_normal_member";

    public static final String role_code_prefix = "role_";

    public static final String redbook_timeout_notify = "redbook_timeout_notify_";

    public static final String role_tenant_creator = "role_tenant_creator";

    public static final String qing_bo_keyword_task = "qing_bo_keyword_task";

    public static final String qing_bo_account_task = "qing_bo_account_task";
}
