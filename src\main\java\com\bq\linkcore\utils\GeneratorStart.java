package com.bq.linkcore.utils;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.converts.MySqlTypeConvert;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.DbColumnType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class GeneratorStart {

    static String db = "********************************************************************************************************************************************************************";
    static String dbName = "data";
    static String dbPwd = "@test-data-plus@^^123";

    static String[] Tables = new String[]{"at_tiktok_author_work_history",
            "at_tiktok_author_work_record"};

    static String userName = "yufeng";

    public static void main(String[] args) {
        String projectPath = System.getProperty("user.dir");
        AutoGenerator mpg = new AutoGenerator();
        mpg.setTemplateEngine(new VelocityTemplateEngine());
        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        gc.setDateType(DateType.TIME_PACK);
        gc.setAuthor(userName);
        gc.setOutputDir(projectPath + "/src/main/java");
        gc.setFileOverride(true);// 是否覆盖同名文件，默认是false
        gc.setActiveRecord(true);// 不需要ActiveRecord特性的请改为false
        gc.setEnableCache(false);// XML 二级缓存
        gc.setBaseResultMap(true);// XML ResultMap
        gc.setBaseColumnList(true);// XML columList
        gc.setSwagger2(true);
        /* 自定义文件命名，注意 %s 会自动填充表实体属性！ */
        gc.setMapperName("%sMapper");
        gc.setEntityName("%sDO");
        gc.setServiceImplName("");
        gc.setControllerName("");
        gc.setServiceName("");
        gc.setXmlName("");
        mpg.setGlobalConfig(gc);
        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDbType(DbType.MYSQL);
        dsc.setTypeConvert(new MySqlTypeConvert() {
            // 自定义数据库表字段类型转换【可选】
            @Override
            public DbColumnType processTypeConvert(GlobalConfig gc, String fieldType) {
                System.out.println("转换类型：" + fieldType);
                if (fieldType.equalsIgnoreCase("tinyint(1)")) {
                    fieldType = "int";
                }
                return (DbColumnType) super.processTypeConvert(gc, fieldType);
            }
        });
        // dsc.setDriverName("com.mysql.jdbc.Driver");
        dsc.setDriverName("com.mysql.cj.jdbc.Driver");
        dsc.setUsername(dbName);
        dsc.setPassword(dbPwd);
        dsc.setUrl(db);
        mpg.setDataSource(dsc);
        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setTablePrefix(new String[]{"t_"});// 此处可以修改为您的表前缀
        strategy.setNaming(NamingStrategy.underline_to_camel);// 表名生成策略
        strategy.setInclude(Tables); // 需要生成的表
        strategy.setEntityLombokModel(true);
        strategy.setRestControllerStyle(true);
        strategy.setCapitalMode(true);// 驼峰命名
        strategy.setSuperEntityClass("");
        strategy.setSuperMapperClass("com.baomidou.mybatisplus.core.mapper.BaseMapper");
        mpg.setStrategy(strategy);
        // 包配置
        PackageConfig pc = new PackageConfig();
        pc.setParent("com.bq.linkcore");
        pc.setEntity("bean.entity");
        pc.setMapper("dao.mapper");
        pc.setController("");
        pc.setService("");
        pc.setServiceImpl("");
        pc.setXml("");
        // 这里还是会在包下生成xml文件，
        mpg.setPackageInfo(pc);
        // 注入自定义配置，可以在 VM 中使用 cfg.abc 【可无】
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                Map<String, Object> map = new HashMap<String, Object>();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                map.put("time", sdf.format(new Date()));
                this.setMap(map);
            }
        };
        //
        List<FileOutConfig> focList = new ArrayList<>();

        // // 关闭默认 xml 生成，调整生成 至 根目录
        TemplateConfig tc = new TemplateConfig();
        tc.setEntity("/template/entity.java.vm");
        tc.setMapper("/template/mapper.java.vm");
        tc.setXml("");
        tc.setController("");
        tc.setService("");
        tc.setServiceImpl("");
        focList.add(new FileOutConfig("/template/mapper.xml.vm") {
            @Override
            public String outputFile(TableInfo tableInfo) {
                return projectPath + "/src/main/resources/mapper/" + tableInfo.getXmlName()
                        + ".xml";
            }
        });
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);
        mpg.setTemplate(tc);
        // 执行生成
        mpg.execute();
        log.info("生成完成！");
    }
}
