package com.bq.linkcore.bean.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @author: lmy
 * @description:
 */
@ApiModel(description = "登录接口请求参数对象")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class UserLoginReqVo implements Serializable {

    @ApiModelProperty(value = "账号，手机号或邮箱")
    @NotNull(message = "请检查email字段，手机号不能为空!")
    private String email;


    @ApiModelProperty(value = "短信验证码")
    private String messageCode;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "获取验证码类型，1=短信登录，2=账户登录 3 = 邀请注册登陆")
    @NotNull(message = "获取验证码类型，1=短信登录，2=账户登录 3 = 邀请注册登陆")
    @Min(1)
    @Max(3)
    private Integer loginType;

    @ApiModelProperty(value = "获取验证码类型，1=登录，2=注册，3=修改密码")
    @NotNull(message = "获取验证码类型，1=登录，2=注册，3=修改密码")
    @Min(1)
    @Max(3)
    private Integer type;

    private String client = "web";
}
