package com.bq.linkcore.services.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.entity.AtTiktokAuthorSearchResultDO;
import com.bq.linkcore.bean.vo.AuthorProfileVo;
import com.bq.linkcore.biz.AtTtSearchBiz;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import com.bq.linkcore.common.RD;
import com.bq.linkcore.common.ResponseMsg;
import com.bq.linkcore.common.ServiceException;
import com.bq.linkcore.services.IUserAuthorService;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import com.bq.linkcore.utils.TikTokHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import static com.bq.linkcore.common.ResponseMsg.ERROR_TIKTOK_AUTHOR_URL;

@Slf4j
@Service
public class UserAuthorServiceImpl implements IUserAuthorService {

    @Resource
    private TikHubTiktokAccountRequester tikHubTiktokAccountRequester;

    @Resource
    private AtTtSearchBiz ttSearchBiz;


    private final static String user_tag = "author-query";

    private final WorkThreadPool authorPullWorker = new WorkThreadPool(
            2,
            4,
            1L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(3000),
            new WorkThreadFactory(user_tag),
            new ThreadRejectPolicy(user_tag));

    @Override
    public ResponseData<AuthorInfo> searchAuthor(Long userId, String homeUrl) {
        try {
            if (userId == null || StringUtils.isBlank(homeUrl)) {
                RD.ok(ResponseMsg.ERROR_INVALID_PARAMS);
            }
            String uniqueId = TikTokHelper.regUniqueId(homeUrl);
            if (StringUtils.isBlank(uniqueId)) {
                return RD.fail(ERROR_TIKTOK_AUTHOR_URL);
            }
            AtTiktokAuthorSearchResultDO existingRecord = ttSearchBiz.queryAuthorSearchResultByUniqueId(uniqueId);
            if (existingRecord != null) {
                AuthorInfo build = AuthorInfo.builder().build();
                BeanUtils.copyProperties(existingRecord, build);
                return RD.ok(build);
            }

            AuthorInfo userProfileWebV1 = tikHubTiktokAccountRequester.getUserProfileWebV1(uniqueId, null);
            if (userProfileWebV1 == null) {
                log.error("获取 tikhub 用户信息失败 :{}", homeUrl);
                return RD.ok(ResponseMsg.FAIL.getCode());
            }
            AtTiktokAuthorSearchResultDO newRecord = convertToSearchResultDO(userProfileWebV1, uniqueId, userId);

            int insertResult = ttSearchBiz.insertAuthorSearchResult(newRecord);
            if (insertResult <= 0) {
                throw new ServiceException(ResponseMsg.FAIL.getCode(), "保存用户信息失败");
            }
            AuthorProfileVo authorProfileVo = new  AuthorProfileVo();
            BeanUtils.copyProperties(newRecord, authorProfileVo);
            return RD.ok(authorProfileVo);
        } catch (ServiceException e) {
            log.error("搜索作者信息失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("搜索作者信息发生未知错误: {}", e.getMessage(), e);
            throw new ServiceException(ResponseMsg.FAIL.getCode(), "搜索作者信息失败");
        }
    }

    private AtTiktokAuthorSearchResultDO convertToSearchResultDO(AuthorInfo authorInfo, String uniqueId, Long userId) {
        return AtTiktokAuthorSearchResultDO.builder()
                .authorId(authorInfo.getAuthorId())
                .uniqueId(uniqueId)
                .authorName(authorInfo.getAuthorName())
                .authorAvatar(authorInfo.getAuthorAvatar())
                .secUid(authorInfo.getSecUid())
                .authorUrl(authorInfo.getAuthorUrl())
                .desc(authorInfo.getDesc())
                .commerceUser(authorInfo.getCommerceUser())
                .commerceCategory(authorInfo.getCategory())
                .friendCount(authorInfo.getFriendCount())
                .followerCount(authorInfo.getFollowerCount())
                .followingCount(authorInfo.getFollowingCount())
                .language(authorInfo.getLanguage())
                .creator(userId)
                .createTime(LocalDateTime.now())
                .updater(userId)
                .updateTime(LocalDateTime.now())
                .isDel(0)
                .build();
    }


    @Override
    public ResponseData addTxAuthorMonitor(Long userId, String authorId) {
        
        return null;
    }

}
