package com.bq.linkcore.services.impl.monitor.provider;

import com.bq.linkcore.services.impl.monitor.TiktokAuthorWorkService;
import com.bq.linkcore.services.impl.monitor.external.TiktokApiService;
import com.bq.linkcore.services.impl.monitor.storage.TiktokDataStorageService;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * TikTok监控提供者
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
public class TiktokMonitoringProvider {

    private final static String TIKTOK_THREAD_TAG = "tiktok-provider";

    /**
     * TikTok专用线程池
     */
    private final WorkThreadPool tiktokThreadPool = new WorkThreadPool(
            3,
            8,
            2L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(5000),
            new WorkThreadFactory(TIKTOK_THREAD_TAG),
            new ThreadRejectPolicy(TIKTOK_THREAD_TAG)
    );

    @Resource
    private TiktokApiService tiktokApiService;

    @Resource
    private TiktokDataStorageService tiktokDataStorageService;

    @Resource
    private TiktokAuthorWorkService tiktokAuthorWorkService;

    /**
     * 处理用户列表 - 综合处理
     */
    public void processUsers(List<String> uniqueIds) {
        log.info("TikTok提供者开始处理用户，数量: {}", uniqueIds.size());
        
        for (String uniqueId : uniqueIds) {
            tiktokThreadPool.addTask(() -> {
                try {
                    processUserInternal(uniqueId);
                } catch (Exception e) {
                    log.error("TikTok提供者处理用户异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }

    /**
     * 处理用户作者信息
     */
    public void processAuthorProfiles(List<String> uniqueIds) {
        log.info("TikTok提供者开始处理作者信息，数量: {}", uniqueIds.size());
        
        for (String uniqueId : uniqueIds) {
            tiktokThreadPool.addTask(() -> {
                try {
                    processAuthorProfileInternal(uniqueId);
                } catch (Exception e) {
                    log.error("TikTok提供者处理作者信息异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }

    /**
     * 处理用户作品
     */
    public void processUserWorks(List<String> uniqueIds) {
        log.info("TikTok提供者开始处理用户作品，数量: {}", uniqueIds.size());
        
        for (String uniqueId : uniqueIds) {
            tiktokThreadPool.addTask(() -> {
                try {
                    processUserWorksInternal(uniqueId);
                } catch (Exception e) {
                    log.error("TikTok提供者处理用户作品异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }

    /**
     * 内部用户处理逻辑
     */
    private void processUserInternal(String uniqueId) {
        log.debug("开始处理TikTok用户: {}", uniqueId);
        
        // 1. 获取用户基本信息
        var userProfile = tiktokApiService.getUserProfile(uniqueId);
        if (userProfile != null) {
            tiktokDataStorageService.saveUserProfile(userProfile);
        }
        
        // 2. 获取用户作品 - 使用现有的TiktokAuthorWorkService
        tiktokAuthorWorkService.processAuthorWorks(uniqueId, null);
        
        log.debug("完成处理TikTok用户: {}", uniqueId);
    }

    /**
     * 内部作者信息处理逻辑
     */
    private void processAuthorProfileInternal(String uniqueId) {
        log.debug("开始处理TikTok作者信息: {}", uniqueId);
        
        var userProfile = tiktokApiService.getUserProfile(uniqueId);
        if (userProfile != null) {
            tiktokDataStorageService.saveUserProfile(userProfile);
            log.debug("成功保存TikTok作者信息: {}", uniqueId);
        } else {
            log.warn("未获取到TikTok作者信息: {}", uniqueId);
        }
    }

    /**
     * 内部用户作品处理逻辑
     */
    private void processUserWorksInternal(String uniqueId) {
        log.debug("开始处理TikTok用户作品: {}", uniqueId);
        
        // 使用现有的TiktokAuthorWorkService处理作品
        tiktokAuthorWorkService.processAuthorWorks(uniqueId, null);
        
        log.debug("提交TikTok用户作品处理任务: {}", uniqueId);
    }

    /**
     * 批量处理用户
     */
    public void batchProcessUsers(List<String> uniqueIds, int batchSize) {
        log.info("TikTok提供者开始批量处理用户，总数量: {}, 批次大小: {}", uniqueIds.size(), batchSize);
        
        for (int i = 0; i < uniqueIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, uniqueIds.size());
            List<String> batch = uniqueIds.subList(i, endIndex);
            
            tiktokThreadPool.addTask(() -> {
                try {
                    processBatchInternal(batch);
                } catch (Exception e) {
                    log.error("TikTok提供者批量处理异常，批次: {}-{}", i, endIndex, e);
                }
            });
        }
    }

    /**
     * 内部批次处理逻辑
     */
    private void processBatchInternal(List<String> batch) {
        log.debug("开始处理TikTok批次，数量: {}", batch.size());
        
        for (String uniqueId : batch) {
            try {
                processUserInternal(uniqueId);
                
                // 添加延迟避免频繁请求
                Thread.sleep(800);
                
            } catch (Exception e) {
                log.error("批次处理单个用户异常，uniqueId: {}", uniqueId, e);
            }
        }
        
        log.debug("完成处理TikTok批次，数量: {}", batch.size());
    }

    /**
     * 处理特定用户的作品监控
     */
    public void processUserWorkMonitoring(String uniqueId, String secUid) {
        tiktokThreadPool.addTask(() -> {
            try {
                log.debug("开始TikTok作品监控: {}", uniqueId);
                tiktokAuthorWorkService.processAuthorWorks(uniqueId, secUid);
            } catch (Exception e) {
                log.error("TikTok作品监控异常，uniqueId: {}", uniqueId, e);
            }
        });
    }

    /**
     * 获取提供者状态
     */
    public String getProviderStatus() {
        String workServiceStatus = tiktokAuthorWorkService.getThreadPoolStatus();
        return String.format("TikTok提供者[任务数: %d, 线程数: %d] + %s", 
                tiktokThreadPool.getTaskCount(), tiktokThreadPool.getThreadCount(), workServiceStatus);
    }

    /**
     * 停止提供者
     */
    public void shutdown() {
        log.info("开始停止TikTok监控提供者");
        tiktokThreadPool.stop();
        log.info("TikTok监控提供者已停止");
    }
}
