package com.bq.linkcore.client.tikhub.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:09
 * @className OneBoundXHSArticle
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TikHubArticleVideo {
    private String videoID;
    private String videoQuality;
    /**
     * VQScore（Video Quality Score）
     *
     * VQScore 是 TikTok 算法对视频质量的量化评分，综合评估以下维度：
     * 画面质量（清晰度、分辨率、稳定性）
     * 音频质量（噪音、音量均衡）
     * 内容吸引力（完播率、互动率）
     * 技术参数（编码效率、压缩率）
     */
    public String vqscore;

    /**
     * Bitrate（比特率） 是数字多媒体领域的核心概念，用于衡量单位时间内传输或处理的数据量。以下是关于比特率的全方位解析
     *
     * 视频比特率
     * 分辨率	推荐比特率范围（H.264）	适用场景
     * 480p	    1,500-3,000 Kbps	    手机小屏观看
     * 1080p	4,000-8,000 Kbps	    短视频平台（如TikTok）
     * 4K	    15,000-50,000 Kbps	    专业影视制作
     *
     * TikTok推荐算法：
     * 视频比特率低于2,000 Kbps可能被判定为低质量（影响VQScore）
     */
    public Long bitrate;

    /**
     * Codec Type（编解码器类型）
     *
     */
    private String codecType;

    /**
     * 540p
     */
    private String definition;

    /**
     * 视频时长
     */
    private Long duration;

    private Long dataSize;

    private Integer height;

    private Integer width;

    /**
     * 封面
     */
    private String cover;

    private String url;
}
