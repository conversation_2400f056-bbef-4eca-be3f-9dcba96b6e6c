<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bq.linkcore.dao.mapper.AtTiktokAuthorWorkRecordMapper">

    <resultMap id="BaseResultMap" type="com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO">
        <id column="id" property="id" />
        <result column="author_id" property="authorId" />
        <result column="unique_id" property="uniqueId" />
        <result column="work_id" property="workId" />
        <result column="work_uuid" property="workUuid" />
        <result column="url" property="url" />
        <result column="download_url" property="downloadUrl" />
        <result column="long_url" property="longUrl" />
        <result column="digest" property="digest" />
        <result column="title" property="title" />
        <result column="thumbnail_link" property="thumbnailLink" />
        <result column="content" property="content" />
        <result column="img_urls" property="imgUrls" />
        <result column="video_urls" property="videoUrls" />
        <result column="music_url" property="musicUrl" />
        <result column="music_author_name" property="musicAuthorName" />
        <result column="music_id" property="musicId" />
        <result column="music_name" property="musicName" />
        <result column="publish_time" property="publishTime" />
        <result column="publish_day" property="publishDay" />
        <result column="location_ip" property="locationIp" />
        <result column="read_count" property="readCount" />
        <result column="like_count" property="likeCount" />
        <result column="comment_count" property="commentCount" />
        <result column="share_count" property="shareCount" />
        <result column="collect_count" property="collectCount" />
        <result column="text_polarity" property="textPolarity" />
        <result column="expired_status" property="expiredStatus" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
    <sql id="Base_Column_List">
        id, author_id, unique_id, work_id, work_uuid, url, download_url, long_url, digest, title, thumbnail_link, content, img_urls, video_urls, music_url, music_author_name, music_id, music_name, publish_time, publish_day, location_ip, read_count, like_count, comment_count, share_count, collect_count, text_polarity, expired_status, update_time, create_time, is_del
    </sql>

  

  
</mapper>
