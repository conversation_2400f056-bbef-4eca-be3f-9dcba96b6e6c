package com.bq.linkcore.client.tikhub.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/22 14:09
 * @className OneBoundXHSArticle
 * @description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TikHubArticle {

    /**
     * 发布账号ID
     */
    private String authorId;

    private String uniqueId;
    /**
     * 作者账号加密ID-可用于拼接主页
     * 对应secUid
     */
    private String secUid;

    /**
     * 作品唯一标识
     */
    private String workId;

    /**
     * 三方生成的一个作品对应的 唯一ID，不清楚唯一性
     */
    private String workUuid;

    /**
     * 作品链接
     */
    private String url;

    private Integer categoryType;

    /**
     * 第一帧图片链接，cover
     */
    private String thumbnailLink;

    /**
     * 图片集,多个用;隔开
     */
    private List<String> imgUrls;

    /**
     * 广告标识：表示当前视频是否为付费广告内容
     *
     * true：该视频是商业推广内容（带有"Sponsored"标签）
     * false：普通用户创作的有机内容
     */
    private Integer isAd;

    /**
     * 作品标题标题
     */
    private String title;

    /**
     * 话题
     */
    private List<String> hashTags;

    /**
     * 作品内容
     */
    private String content;

    /**
     * 发布时间
     */
    private Integer publishTime;

    private String textLanguage;

    /**
     * 发布地理位置
     */
    private String locationIp="";

    /**
     * 阅读量
     */
    private Integer playCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 转发数
     */
    private Integer shareCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 视频video
     */
    private TikHubArticleVideo video;

    /**
     * 背景音乐信息
     */
    private TikHubArticleMusic music;
}
