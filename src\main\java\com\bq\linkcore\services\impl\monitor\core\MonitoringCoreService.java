package com.bq.linkcore.services.impl.monitor.core;

import com.bq.linkcore.services.impl.monitor.provider.DouyinMonitoringProvider;
import com.bq.linkcore.services.impl.monitor.provider.TiktokMonitoringProvider;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 监控核心服务 - 任务编排与通用逻辑
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
public class MonitoringCoreService {

    private final static String CORE_THREAD_TAG = "monitoring-core";

    /**
     * 核心业务线程池
     */
    private final WorkThreadPool coreThreadPool = new WorkThreadPool(
            5,
            10,
            3L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(10000),
            new WorkThreadFactory(CORE_THREAD_TAG),
            new ThreadRejectPolicy(CORE_THREAD_TAG)
    );

    @Resource
    private DouyinMonitoringProvider douyinMonitoringProvider;

    @Resource
    private TiktokMonitoringProvider tiktokMonitoringProvider;

    /**
     * 执行监控任务 - 主入口
     * 
     * @param taskType 任务类型 (douyin/tiktok)
     * @param uniqueIds 用户唯一标识列表
     */
    public void executeMonitoringTask(String taskType, List<String> uniqueIds) {
        log.info("开始执行监控任务，任务类型: {}, 用户数量: {}", taskType, uniqueIds.size());
        
        // 提交到核心线程池进行任务编排
        coreThreadPool.addTask(() -> {
            try {
                dispatchTask(taskType, uniqueIds);
            } catch (Exception e) {
                log.error("执行监控任务异常，任务类型: {}", taskType, e);
            }
        });
    }

    /**
     * 任务分派逻辑
     */
    private void dispatchTask(String taskType, List<String> uniqueIds) {
        switch (taskType.toLowerCase()) {
            case "douyin":
                log.info("分派抖音监控任务，用户数量: {}", uniqueIds.size());
                douyinMonitoringProvider.processUsers(uniqueIds);
                break;
                
            case "tiktok":
                log.info("分派TikTok监控任务，用户数量: {}", uniqueIds.size());
                tiktokMonitoringProvider.processUsers(uniqueIds);
                break;
                
            case "all":
                log.info("分派全平台监控任务，用户数量: {}", uniqueIds.size());
                // 并行处理两个平台
                coreThreadPool.addTask(() -> douyinMonitoringProvider.processUsers(uniqueIds));
                coreThreadPool.addTask(() -> tiktokMonitoringProvider.processUsers(uniqueIds));
                break;
                
            default:
                log.warn("未知的任务类型: {}", taskType);
                break;
        }
    }

    /**
     * 执行作者信息监控任务
     */
    public void executeAuthorMonitoringTask(String taskType, List<String> uniqueIds) {
        log.info("开始执行作者信息监控任务，任务类型: {}, 用户数量: {}", taskType, uniqueIds.size());
        
        coreThreadPool.addTask(() -> {
            try {
                dispatchAuthorTask(taskType, uniqueIds);
            } catch (Exception e) {
                log.error("执行作者信息监控任务异常，任务类型: {}", taskType, e);
            }
        });
    }

    /**
     * 作者信息任务分派
     */
    private void dispatchAuthorTask(String taskType, List<String> uniqueIds) {
        switch (taskType.toLowerCase()) {
            case "douyin":
                douyinMonitoringProvider.processAuthorProfiles(uniqueIds);
                break;
                
            case "tiktok":
                tiktokMonitoringProvider.processAuthorProfiles(uniqueIds);
                break;
                
            case "all":
                coreThreadPool.addTask(() -> douyinMonitoringProvider.processAuthorProfiles(uniqueIds));
                coreThreadPool.addTask(() -> tiktokMonitoringProvider.processAuthorProfiles(uniqueIds));
                break;
                
            default:
                log.warn("未知的作者信息任务类型: {}", taskType);
                break;
        }
    }

    /**
     * 执行作品监控任务
     */
    public void executeWorkMonitoringTask(String taskType, List<String> uniqueIds) {
        log.info("开始执行作品监控任务，任务类型: {}, 用户数量: {}", taskType, uniqueIds.size());
        
        coreThreadPool.addTask(() -> {
            try {
                dispatchWorkTask(taskType, uniqueIds);
            } catch (Exception e) {
                log.error("执行作品监控任务异常，任务类型: {}", taskType, e);
            }
        });
    }

    /**
     * 作品任务分派
     */
    private void dispatchWorkTask(String taskType, List<String> uniqueIds) {
        switch (taskType.toLowerCase()) {
            case "douyin":
                douyinMonitoringProvider.processUserWorks(uniqueIds);
                break;
                
            case "tiktok":
                tiktokMonitoringProvider.processUserWorks(uniqueIds);
                break;
                
            case "all":
                coreThreadPool.addTask(() -> douyinMonitoringProvider.processUserWorks(uniqueIds));
                coreThreadPool.addTask(() -> tiktokMonitoringProvider.processUserWorks(uniqueIds));
                break;
                
            default:
                log.warn("未知的作品任务类型: {}", taskType);
                break;
        }
    }

    /**
     * 获取核心服务状态
     */
    public MonitoringCoreStatus getCoreStatus() {
        return MonitoringCoreStatus.builder()
                .threadPoolTaskCount(coreThreadPool.getTaskCount())
                .threadPoolThreadCount(coreThreadPool.getThreadCount())
                .douyinProviderStatus(douyinMonitoringProvider.getProviderStatus())
                .tiktokProviderStatus(tiktokMonitoringProvider.getProviderStatus())
                .build();
    }

    /**
     * 停止核心服务
     */
    public void shutdown() {
        log.info("开始停止监控核心服务");
        coreThreadPool.stop();
        douyinMonitoringProvider.shutdown();
        tiktokMonitoringProvider.shutdown();
        log.info("监控核心服务已停止");
    }
}
