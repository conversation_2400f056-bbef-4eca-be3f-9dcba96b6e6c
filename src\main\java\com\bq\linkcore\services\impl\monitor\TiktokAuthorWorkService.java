package com.bq.linkcore.services.impl.monitor;

import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkHistoryDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.bean.entity.TiktokArticleMusicsDO;
import com.bq.linkcore.bean.entity.TiktokArticleVideosDO;
import com.bq.linkcore.biz.AtTtAuthorWorkBiz;
import com.bq.linkcore.client.tikhub.models.TikHubArticle;
import com.bq.linkcore.client.tikhub.models.TikHubArticleMusic;
import com.bq.linkcore.client.tikhub.models.TikHubArticleVideo;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import com.bq.linkcore.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * TikTok作者作品数据处理服务
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Slf4j
@Service
public class TiktokAuthorWorkService {

    private final static String WORK_THREAD_TAG = "tiktok-work-processor";
    /**
     * 线程池配置
     */
    private final WorkThreadPool workThreadPool = new WorkThreadPool(
            2,
            6,
            2L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(5000),
            new WorkThreadFactory(WORK_THREAD_TAG),
            new ThreadRejectPolicy(WORK_THREAD_TAG)
    );
    @Resource
    private TikHubTiktokAccountRequester tikHubTiktokAccountRequester;
    @Resource
    private AtTtAuthorWorkBiz atTtAuthorWorkBiz;

    /**
     * 处理作者作品数据
     *
     * @param uniqueId 用户唯一标识
     * @param secUid   用户加密ID
     */
    public void processAuthorWorks(String uniqueId, String secUid) {
        log.info("开始处理作者作品数据，uniqueId: {}, secUid: {}", uniqueId, secUid);

        workThreadPool.addTask(() -> {
            try {
                processAuthorWorksInternal(uniqueId, secUid);
            } catch (Exception e) {
                log.error("处理作者作品数据异常，uniqueId: {}, secUid: {}", uniqueId, secUid, e);
            }
        });
    }

    private void processAuthorWorksInternal(String uniqueId, String secUid) {
        List<TikHubArticle> tikHubArticleList = getArticleDetailsWithMock(uniqueId, secUid);

        if (CollectionUtils.isEmpty(tikHubArticleList)) {
            log.warn("未获取到作品数据，uniqueId: {}, secUid: {}", uniqueId, secUid);
            return;
        }

        log.info("获取到作品数据 {} 条，uniqueId: {}", tikHubArticleList.size(), uniqueId);

        String recordDay = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        for (TikHubArticle tikHubArticle : tikHubArticleList) {
            try {
                AtTiktokAuthorWorkRecordDO workRecordDO = processWorkRecord(tikHubArticle, uniqueId);

                processWorkHistory(workRecordDO, recordDay);
            } catch (Exception e) {
                log.error("处理单个作品数据异常，workId: {}, uniqueId: {}",
                        tikHubArticle.getWorkId(), uniqueId, e);
            }
        }

        log.info("完成处理作者作品数据，uniqueId: {}, 处理数量: {}", uniqueId, tikHubArticleList.size());
    }

    /**
     * 获取作品详情（包含mock数据）
     */
    private List<TikHubArticle> getArticleDetailsWithMock(String uniqueId, String secUid) {
        try {
            // 调用真实API（目前返回空列表）
            List<TikHubArticle> realData = tikHubTiktokAccountRequester.getUserArticlesByWebV1(uniqueId, secUid, 50);

            // 如果真实API有数据，直接返回
            if (realData != null && !realData.isEmpty()) {
                return realData;
            }

            // 否则返回mock数据
            return generateMockArticleDetails(uniqueId);

        } catch (Exception e) {
            log.error("获取作品详情异常，uniqueId: {}, secUid: {}", uniqueId, secUid, e);
            // 异常情况下也返回mock数据
            return generateMockArticleDetails(uniqueId);
        }
    }

    /**
     * 生成mock数据
     */
    private List<TikHubArticle> generateMockArticleDetails(String uniqueId) {
        List<TikHubArticle> mockData = new ArrayList<>();
        Random random = new Random();

        // 生成3-8个mock作品
        int count = 3 + random.nextInt(6);

        for (int i = 1; i <= count; i++) {
            TikHubArticle article = TikHubArticle.builder()
                    .authorId("mock_author_" + uniqueId)
                    .uniqueId(uniqueId)
                    .workId("mock_work_" + uniqueId + "_" + i + "_" + System.currentTimeMillis())
                    .workUuid("mock_uuid_" + uniqueId + "_" + i)
                    .url("https://tiktok.com/video/" + i)
                    .title("Mock作品标题 " + i)
                    .thumbnailLink("https://thumbnail.tiktok.com/" + i + ".jpg")
                    .content("这是第" + i + "个mock作品的内容描述，包含了丰富的文本信息。")
                    .imgUrls(Arrays.asList("https://img1.tiktok.com/" + i + ".jpg;https://img2.tiktok.com/" + i + ".jpg"))
                    .publishTime(1752465350)
                    .locationIp("Mock Location " + i)
                    .playCount(1000 + random.nextInt(50000))
                    .likeCount(100 + random.nextInt(5000))
                    .commentCount(10 + random.nextInt(500))
                    .shareCount(5 + random.nextInt(100))
                    .collectCount(2 + random.nextInt(50))
                    .categoryType(112)
                    .isAd(0)
                    .hashTags(Arrays.asList("collegefootball", "cfb", "football", "arizonastate"))
                    .textLanguage("en")
                    .video(TikHubArticleVideo.builder()
                            .videoID("vid_" + System.currentTimeMillis())
                            .videoQuality("normal")
                            .vqscore(String.format("%.1f", 70 + ThreadLocalRandom.current().nextDouble() * 30)) // 70-100之间的随机分数
                            .bitrate(1778909L)
                            .codecType("h264")
                            .definition("540p")
                            .duration(ThreadLocalRandom.current().nextLong(15000, 180000)) // 15-180秒之间的随机时长
                            .dataSize(ThreadLocalRandom.current().nextLong(5000000, 50000000)) // 5MB-50MB
                            .height(1024)
                            .width(576)
                            .cover("https://p16-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-p-0068-tx2/okICVFDeREEUZQGBZpEQ8lBAEDdAt2gIdofylL~tplv-tiktokx-origin.image?dr=9636&x-expires=1752656400&x-signature=K8d7qcLthcJXoWwoU%2B%2F8ro9NgTI%3D&t=4d5b0474&ps=13740610&shp=81f88b70&shcp=43f4a2f9&idc=useast5")
                            .url("https://api16-normal-useast5.tiktokv.us/aweme/v1/play/?video_id=v15044gf0000d1q8igfog65r0nggtisg&line=0&watermark=1&logo_name=tiktok_m&source=PUBLISH&file_id=4818f77e9a454222b4ebca9b11de3092&item_id=7526791872807734559&signaturev3=dmlkZW9faWQ7ZmlsZV9pZDtpdGVtX2lkLmNhZDk4YmM3OTAwOTU4ZDlmNmI3MjI1MzY4ZTliM2Fm&shp=d05b14bd&shcp=-")
                            .build())
                    .music(TikHubArticleMusic.builder()
                            .musicId("mus_" + System.currentTimeMillis())
                            .title("original sound")
                            .authorName("mock_name_" + uniqueId + "_" + i)
                            .authorAvatar("https://p19-pu-sign-useast8.tiktokcdn-us.com/tos-useast8-avt-0068-tx2/989539083516b79d3c9da345688befb8~tplv-tiktokx-cropcenter:100:100.jpeg?dr=9640&refresh_token=88d99de5&x-expires=1752656400&x-signature=ghMETkYzupizj7Jzmy3%2B4%2BvMaJo%3D&t=4d5b0474&ps=13740610&shp=a5d48078&shcp=81f88b70&idc=useast5")
                            .duration(ThreadLocalRandom.current().nextInt(30, 240)) // 30秒到4分钟
                            .isCopyrighted(0)
                            .original(1)
                            .url("https://v16m.tiktokcdn-us.com/b25cbc147ac51c96f7a143a281be5607/68751ff6/video/tos/useast8/tos-useast8-v-27dcd7-tx2/okCABEA5abBJSADVIAAhlEacid0M7R5iWviHP/?a=1233&bti=ODszNWYuMDE6&ch=0&cr=0&dr=0&er=0&lr=default&cd=0%7C0%7C0%7C0&br=250&bt=125&ds=5&ft=GSDrKInz7ThhzpWKXq8Zmo&mime_type=audio_mpeg&qs=13&rc=M2k3cHM5cnQ7NDMzaTU8NEBpM2k3cHM5cnQ7NDMzaTU8NEBibXMuMmQ0Zm5hLS1kMTJzYSNibXMuMmQ0Zm5hLS1kMTJzcw%3D%3D&vvpl=1&l=202507140918584060924211977501BE7C&btag=e00078000")
                            .build())
                    .build();

            mockData.add(article);
        }

        log.info("生成mock数据 {} 条，uniqueId: {}", mockData.size(), uniqueId);
        return mockData;
    }

    @Transactional(readOnly = false, rollbackFor = {RuntimeException.class, Exception.class})
    private AtTiktokAuthorWorkRecordDO processWorkRecord(TikHubArticle tikHubArticle, String uniqueId) {
        TikHubArticleVideo tikHubArticleVideo = tikHubArticle.getVideo();
        // 插入video
        TiktokArticleVideosDO articleVideosDO = atTtAuthorWorkBiz.queryArticleVideo(tikHubArticleVideo.getVideoID());
        if (articleVideosDO != null) {
            atTtAuthorWorkBiz.insertArticleVideo(buildArticleVideoDO(tikHubArticleVideo));
        }

        // 插入music
        TikHubArticleMusic tikHubArticleMusic = tikHubArticle.getMusic();
        // 插入video
        TiktokArticleMusicsDO articleMusicDO = atTtAuthorWorkBiz.queryArticleMusic(tikHubArticleMusic.getMusicId());
        if (articleMusicDO != null) {
            atTtAuthorWorkBiz.insertArticleMusic(buildArticleMusicDO(tikHubArticleMusic));
        }

        String workId = tikHubArticle.getWorkId();
        // 先查询是否存在
        AtTiktokAuthorWorkRecordDO existingRecord = atTtAuthorWorkBiz.queryAuthorWorkRecordByWorkId(workId);
        if (existingRecord != null) {
            log.debug("作品记录已存在，跳过插入，workId: {}", workId);
            return existingRecord;
        }

        // 构建记录对象
        AtTiktokAuthorWorkRecordDO recordDO = buildWorkRecordDO(tikHubArticle, uniqueId);
        // 插入数据
        int result = atTtAuthorWorkBiz.insertAuthorWorkRecord(recordDO);
        if (result > 0) {
            log.debug("成功插入作品记录，workId: {}", workId);
        } else {
            log.warn("插入作品记录失败，workId: {}", workId);
        }

        return recordDO;
    }

    @Transactional(readOnly = false, rollbackFor = {RuntimeException.class, Exception.class})
    private void processWorkHistory(AtTiktokAuthorWorkRecordDO authorWorkRecordDO, String recordDay) {
        String workId = authorWorkRecordDO.getWorkId();

        // 先查询是否存在
        AtTiktokAuthorWorkHistoryDO existingHistory = atTtAuthorWorkBiz
                .queryAuthorWorkHistoryByWorkIdAndRecordDay(workId, recordDay);
        if (existingHistory != null) {
            AtTiktokAuthorWorkHistoryDO n = buildWorkHistoryDO(authorWorkRecordDO, recordDay);
            n.setId(existingHistory.getId());
            n.setCreator(existingHistory.getCreator());
            n.setCreateTime(existingHistory.getCreateTime());
            n.setUpdateTime(LocalDateTime.now());

            atTtAuthorWorkBiz.updateAuthorWorkHistory(n);
            return;
        }

        // 构建历史记录对象
        AtTiktokAuthorWorkHistoryDO historyDO = buildWorkHistoryDO(authorWorkRecordDO, recordDay);
        // 插入数据
        int result = atTtAuthorWorkBiz.insertAuthorWorkHistory(historyDO);
        if (result > 0) {
            log.debug("成功插入作品历史记录，workId: {}, recordDay: {}", workId, recordDay);
        } else {
            log.warn("插入作品历史记录失败，workId: {}, recordDay: {}", workId, recordDay);
        }
    }

    /**
     * 构建作品记录DO对象
     */
    private AtTiktokAuthorWorkRecordDO buildWorkRecordDO(TikHubArticle tikHubArticle, String uniqueId) {
        AtTiktokAuthorWorkRecordDO recordDO = new AtTiktokAuthorWorkRecordDO();

        // 复制基本属性
        BeanUtils.copyProperties(tikHubArticle, recordDO);

        // 设置特定字段
        recordDO.setUniqueId(uniqueId);
        recordDO.setImages(StringUtil.arrayToStr(tikHubArticle.getImgUrls()));
        recordDO.setHashtags(StringUtil.arrayToStr(tikHubArticle.getHashTags()));

        recordDO.setMusicId(tikHubArticle.getMusic().getMusicId());
        recordDO.setVideoId(tikHubArticle.getVideo().getVideoID());

        recordDO.setCreator(0L);
        recordDO.setUpdater(0L);
        recordDO.setCreateTime(LocalDateTime.now());
        recordDO.setUpdateTime(LocalDateTime.now());

        return recordDO;
    }


    private TiktokArticleVideosDO buildArticleVideoDO(TikHubArticleVideo tikHubArticleVideo) {
        TiktokArticleVideosDO videosDO = new TiktokArticleVideosDO();

        // 复制基本属性
        BeanUtils.copyProperties(tikHubArticleVideo, videosDO);

        return videosDO;
    }

    private TiktokArticleMusicsDO buildArticleMusicDO(TikHubArticleMusic tikHubArticleMusic) {
        TiktokArticleMusicsDO musicsDO = new TiktokArticleMusicsDO();

        // 复制基本属性
        BeanUtils.copyProperties(tikHubArticleMusic, musicsDO);

        return musicsDO;
    }

    /**
     * 构建作品历史记录DO对象
     */
    private AtTiktokAuthorWorkHistoryDO buildWorkHistoryDO(AtTiktokAuthorWorkRecordDO authorWorkRecordDO, String recordDay) {
        AtTiktokAuthorWorkHistoryDO historyDO = new AtTiktokAuthorWorkHistoryDO();

        // 复制基本属性
        BeanUtils.copyProperties(authorWorkRecordDO, historyDO);

        // 设置特定字段
        historyDO.setRecordDay(recordDay);
        historyDO.setCreateTime(LocalDateTime.now());
        historyDO.setUpdateTime(LocalDateTime.now());

        return historyDO;
    }

    /**
     * 批量处理作者作品数据
     *
     * @param uniqueIds 用户唯一标识列表
     */
    public void batchProcessAuthorWorks(List<String> uniqueIds) {
        if (uniqueIds == null || uniqueIds.isEmpty()) {
            log.warn("批量处理作品数据：用户列表为空");
            return;
        }

        log.info("开始批量处理作者作品数据，用户数量: {}", uniqueIds.size());

        for (String uniqueId : uniqueIds) {
            try {
                processAuthorWorks(uniqueId, null);

                // 添加延迟避免过于频繁的请求
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("批量处理作品数据异常，uniqueId: {}", uniqueId, e);
            }
        }

        log.info("完成批量处理作者作品数据，用户数量: {}", uniqueIds.size());
    }

    /**
     * 获取线程池状态信息
     */
    public String getThreadPoolStatus() {
        return String.format("线程池状态 - 任务数: %d, 线程数: %d",
                workThreadPool.getTaskCount(), workThreadPool.getThreadCount());
    }
}
