package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-14 16:51:10
 * @ClassName: AtTiktokAuthorWorkHistoryDO
 * @Description: 账户监控-tiktok 作品表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("at_tiktok_author_work_history")
public class AtTiktokAuthorWorkHistoryDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 发布账号ID
     */
    private String authorId;

    /**
     * 作者账号加密ID-可用于拼接主页
     */
    private String uniqueId;

    /**
     * 作品唯一标识
     */
    private String workId;

    /**
     * 三方生成的一个作品对应的 唯一ID，不清楚唯一性
     */
    private String workUuid;

    /**
     * 作品链接
     */
    private String url;

    /**
     * 下载链接
     */
    private String downloadUrl;

    /**
     * 作品长链接，有效期一个月
     */
    private String longUrl;

    /**
     * 摘要
     */
    private String digest;

    /**
     * 作品标题标题
     */
    private String title;

    /**
     * 第一帧图片链接
     */
    private String thumbnailLink;

    /**
     * 作品内容
     */
    private String content;

    /**
     * 图片集,多个用;隔开
     */
    private String imgUrls;

    /**
     * 视频url 集合
     */
    private String videoUrls;

    /**
     * 音乐链接
     */
    private String musicUrl;

    /**
     * 音乐作者
     */
    private String musicAuthorName;

    /**
     * 音乐ID
     */
    private String musicId;

    /**
     * 音乐名字
     */
    private String musicName;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 发布日期
     */
    private String publishDay;

    /**
     * 发布地理位置
     */
    private String locationIp;

    /**
     * 阅读量
     */
    private Integer readCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 转发数
     */
    private Integer shareCount;

    /**
     * 收藏数
     */
    private Integer collectCount;

    /**
     * 作品的感情级别性：1=正、0=中、-1=负
     */
    private Integer textPolarity;

    /**
     * 作品及评论能否更新 默认发布作品后 15 天 0 =未过期 1= 已经过期
     */
    private Integer expiredStatus;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 数据获取日期
     */
    private String recordDay;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 是否删除（0未删除，1已删除）
     */
    private Integer isDel;




}
