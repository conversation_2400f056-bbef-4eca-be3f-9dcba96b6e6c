package com.bq.linkcore.services;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.AuthorProfileVo;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;

public interface IUserAuthorService {
    /**
     * 搜索达人
     * @param userId
     * @param homeUrl
     * @return
     */
    ResponseData<AuthorInfo> searchAuthor(Long userId, String homeUrl);

    /**
     * 添加达人
     * @param userId
     * @param authorId
     * @return
     */
    ResponseData addTxAuthorMonitor(Long userId, String authorId);



}
