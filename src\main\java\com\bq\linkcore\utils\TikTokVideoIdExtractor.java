package com.bq.linkcore.utils;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/7/2 12:26
 * @className TikTokVideoIdExtractor
 * @description
 */
public class TikTokVideoIdExtractor {
    public static String getTikTokVideoId(String urlString) {
        try {
            // 如果URL是短链接，解析重定向后的真实URL
            if (isShortUrl(urlString)) {
                urlString = resolveShortUrl(urlString);
            }

            // 从最终URL中提取视频ID
            return extractVideoIdFromUrl(urlString);
        } catch (Exception e) {
            return null;
        }
    }

    private static boolean isShortUrl(String url) {
        // 检查是否是TikTok短链接格式
        Pattern pattern = Pattern.compile("https?://(vm|vt)\\.tiktok\\.com/\\w+/?");
        return pattern.matcher(url).matches();
    }

    private static String resolveShortUrl(String shortUrl) throws IOException {
        HttpURLConnection connection = null;
        try {
            URL url = new URL(shortUrl);
            connection = (HttpURLConnection) url.openConnection();

            // 设置请求参数
            connection.setRequestMethod("HEAD");
            connection.setInstanceFollowRedirects(false); // 禁用自动重定向
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            // 获取重定向地址
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_MOVED_PERM ||
                    responseCode == HttpURLConnection.HTTP_MOVED_TEMP) {

                String location = connection.getHeaderField("Location");
                if (location != null && !location.isEmpty()) {
                    return location;
                }
            }

            throw new IOException("Failed to resolve redirect. Response code: " + responseCode);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    private static String extractVideoIdFromUrl(String url) {
        Pattern pattern = Pattern.compile(
                        "(?:/video/(\\d+)" +
                        "|\\?item_id=(\\d+)" +
                        "|/v/(\\d+)\\.html)"
        );

        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            // 返回第一个非空匹配组
            for (int i = 1; i <= matcher.groupCount(); i++) {
                if (matcher.group(i) != null) {
                    return matcher.group(i);
                }
            }
        }
        throw new IllegalArgumentException("Invalid TikTok URL format: " + url);
    }

    public static void main(String[] args) {
        // 测试示例
        String[] testUrls = {
                "https://vm.tiktok.com/ZM6x5gS9u/",                  // 短链接
                "https://www.tiktok.com/@user/video/1234567890123456789", // 标准长链接
                "https://m.tiktok.com/v/1234567890123456789.html",    // 移动端长链接
                "https://www.tiktok.com/t/ZTRQeQd3m/?item_id=1234567890123456789" // 带item_id参数
        };

        for (String url : testUrls) {
            try {
                System.out.println("URL: " + url);
                System.out.println("Work ID: " + getTikTokVideoId(url));
                System.out.println("----------------------");
            } catch (Exception e) {
                System.err.println("Error processing " + url + ": " + e.getMessage());
            }
        }
    }
}
