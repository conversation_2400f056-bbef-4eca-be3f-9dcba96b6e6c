package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkHistoryDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorWorkRecordDO;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorWorkHistoryMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorWorkRecordMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class AtTtAuthorWorkBiz {

    @Resource
    private AtTiktokAuthorWorkRecordMapper tiktokAuthorWorkRecordMapper;

    @Resource
    private AtTiktokAuthorWorkHistoryMapper tiktokAuthorWorkHistoryMapper;

    // ==================== AtTiktokAuthorWorkRecord 相关方法 ====================

    /**
     * 插入TikTok作者作品记录
     * @param authorWorkRecordDO TikTok作者作品记录实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorWorkRecord(AtTiktokAuthorWorkRecordDO authorWorkRecordDO) {
        return tiktokAuthorWorkRecordMapper.insert(authorWorkRecordDO);
    }

    /**
     * 根据uniqueId查询TikTok作者作品记录列表
     * @param uniqueId 用户名
     * @return TikTok作者作品记录列表
     */
    public List<AtTiktokAuthorWorkRecordDO> queryAuthorWorkRecordListByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO>()
                .eq(AtTiktokAuthorWorkRecordDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkRecordDO::getIsDel, 0)
                .orderByDesc(AtTiktokAuthorWorkRecordDO::getPublishTime);
        return tiktokAuthorWorkRecordMapper.selectList(queryWrapper);
    }

    /**
     * 根据workId查询TikTok作者作品记录
     * @param workId 作品唯一标识
     * @return TikTok作者作品记录实体对象，不存在则返回null
     */
    public AtTiktokAuthorWorkRecordDO queryAuthorWorkRecordByWorkId(String workId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO>()
                .eq(AtTiktokAuthorWorkRecordDO::getWorkId, workId)
                .eq(AtTiktokAuthorWorkRecordDO::getIsDel, 0);
        return tiktokAuthorWorkRecordMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId和workId查询记录是否存在
     * @param uniqueId 用户名
     * @param workId 作品唯一标识
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorWorkRecordExistsByUniqueIdAndWorkId(String uniqueId, String workId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkRecordDO>()
                .eq(AtTiktokAuthorWorkRecordDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkRecordDO::getWorkId, workId)
                .eq(AtTiktokAuthorWorkRecordDO::getIsDel, 0);
        return tiktokAuthorWorkRecordMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新TikTok作者作品记录
     * @param authorWorkRecordDO TikTok作者作品记录实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorWorkRecord(AtTiktokAuthorWorkRecordDO authorWorkRecordDO) {
        return tiktokAuthorWorkRecordMapper.updateById(authorWorkRecordDO);
    }

    // ==================== AtTiktokAuthorWorkHistory 相关方法 ====================

    /**
     * 插入TikTok作者作品历史记录
     * @param authorWorkHistoryDO TikTok作者作品历史记录实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorWorkHistory(AtTiktokAuthorWorkHistoryDO authorWorkHistoryDO) {
        return tiktokAuthorWorkHistoryMapper.insert(authorWorkHistoryDO);
    }

    /**
     * 根据uniqueId和recordDay查询TikTok作者作品历史记录列表
     * @param uniqueId 用户名
     * @param recordDay 数据获取日期
     * @return TikTok作者作品历史记录列表
     */
    public List<AtTiktokAuthorWorkHistoryDO> queryAuthorWorkHistoryListByUniqueIdAndRecordDay(String uniqueId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO>()
                .eq(AtTiktokAuthorWorkHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkHistoryDO::getRecordDay, recordDay)
                .eq(AtTiktokAuthorWorkHistoryDO::getIsDel, 0)
                .orderByDesc(AtTiktokAuthorWorkHistoryDO::getPublishTime);
        return tiktokAuthorWorkHistoryMapper.selectList(queryWrapper);
    }

    /**
     * 根据uniqueId查询TikTok作者作品历史记录列表
     * @param uniqueId 用户名
     * @return TikTok作者作品历史记录列表
     */
    public List<AtTiktokAuthorWorkHistoryDO> queryAuthorWorkHistoryListByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO>()
                .eq(AtTiktokAuthorWorkHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkHistoryDO::getIsDel, 0)
                .orderByDesc(AtTiktokAuthorWorkHistoryDO::getRecordDay)
                .orderByDesc(AtTiktokAuthorWorkHistoryDO::getPublishTime);
        return tiktokAuthorWorkHistoryMapper.selectList(queryWrapper);
    }

    /**
     * 根据workId和recordDay查询TikTok作者作品历史记录
     * @param workId 作品唯一标识
     * @param recordDay 数据获取日期
     * @return TikTok作者作品历史记录实体对象，不存在则返回null
     */
    public AtTiktokAuthorWorkHistoryDO queryAuthorWorkHistoryByWorkIdAndRecordDay(String workId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO>()
                .eq(AtTiktokAuthorWorkHistoryDO::getWorkId, workId)
                .eq(AtTiktokAuthorWorkHistoryDO::getRecordDay, recordDay)
                .eq(AtTiktokAuthorWorkHistoryDO::getIsDel, 0);
        return tiktokAuthorWorkHistoryMapper.selectOne(queryWrapper);
    }

    /**
     * 根据uniqueId、workId和recordDay查询记录是否存在
     * @param uniqueId 用户名
     * @param workId 作品唯一标识
     * @param recordDay 数据获取日期
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorWorkHistoryExistsByUniqueIdAndWorkIdAndRecordDay(String uniqueId, String workId, String recordDay) {
        LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorWorkHistoryDO>()
                .eq(AtTiktokAuthorWorkHistoryDO::getUniqueId, uniqueId)
                .eq(AtTiktokAuthorWorkHistoryDO::getWorkId, workId)
                .eq(AtTiktokAuthorWorkHistoryDO::getRecordDay, recordDay)
                .eq(AtTiktokAuthorWorkHistoryDO::getIsDel, 0);
        return tiktokAuthorWorkHistoryMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新TikTok作者作品历史记录
     * @param authorWorkHistoryDO TikTok作者作品历史记录实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorWorkHistory(AtTiktokAuthorWorkHistoryDO authorWorkHistoryDO) {
        return tiktokAuthorWorkHistoryMapper.updateById(authorWorkHistoryDO);
    }

}
