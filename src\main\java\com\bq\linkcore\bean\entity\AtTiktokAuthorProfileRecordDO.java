package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-14 16:16:48
 * @ClassName: AtTiktokAuthorProfileRecordDO
 * @Description: TikTok达人历史信息表
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("at_tiktok_author_profile_record")
public class AtTiktokAuthorProfileRecordDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 发布账号ID
     */
    private String authorId;

    /**
     * 用户名
     */
    private String uniqueId;

    /**
     * 作者昵称
     */
    private String authorName;

    /**
     * 作者头像URL
     */
    private String authorAvatar;

    /**
     * 作者账号加密ID(secUid)
     */
    private String secUid;

    /**
     * 发布者主页地址
     */
    private String authorUrl;

    /**
     * 个人简介
     */
    private String desc;

    /**
     * 账号创建时间戳
     */
    private Integer registerTime;

    /**
     * 是否认证(1:是,0:否)
     */
    private Integer isVerified;

    /**
     * 用户注册地区(如US)
     */
    private String region;

    /**
     * 用户界面语言(如en)
     */
    private String language;

    /**
     * 是否私密账号(1:是,0:否)
     */
    private Integer privateAccount;

    /**
     * 是否开通电商功能(1:是,0:否)
     */
    private Integer commerceUser;

    /**
     * 是否TikTok小店卖家(1:是,0:否)
     */
    private Integer ttSeller;

    /**
     * 开通电商的类目
     */
    private String commerceCategory;

    /**
     * 粉丝数
     */
    private Integer followerCount;

    /**
     * 关注数
     */
    private Integer followingCount;

    /**
     * 总获赞数
     */
    private Integer heartCount;

    /**
     * 发布视频数
     */
    private Integer videoCount;

    /**
     * 好友数
     */
    private Integer friendCount;

    /**
     * 刷新时间
     */
    private LocalDateTime refreshTime;

    /**
     * 创建者
     */
    private Long creator;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新人
     */
    private Long updater;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除（0未删除，1已删除）
     */
    private Integer isDel;




}
