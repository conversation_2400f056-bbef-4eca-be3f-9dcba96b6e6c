package com.bq.linkcore.client.tikhub.tiktok;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bq.linkcore.client.tikhub.models.ArticleDetailModel;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.utils.DateTimeUtil;
import com.bq.linkcore.utils.StringUtil;
import com.bq.linkcore.utils.TikTokVideoIdExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/29 16:32
 * @className TikHubDouYinClient
 * @description
 */

@Slf4j
@Component
public class TikHubTiktokAccountRequester {
    @Resource(name = "commonRestTemplate")
    private RestTemplate restTemplate;

    public AuthorInfo getUserProfileWebV1(String uniqueId, String secUid) {
        String url = "https://api.tikhub.io/api/v1/tiktok/web/fetch_user_profile?";
        if (StringUtils.isNotBlank(uniqueId)) {
            url += "uniqueId=" + uniqueId;
        } else {
            if (StringUtils.isNotBlank(secUid)) {
                url += "secUid=" + secUid;
            } else {
                return null;
            }
        }

        JSONObject object = callGet(url);
        if (object == null) {
            return null;
        }

        AuthorInfo authorInfo = new AuthorInfo();
        try {
            JSONObject data = object.getJSONObject("data");
            Integer code = data.getInteger("statusCode");
            if (code != 0) {
                return null;
            }

            JSONObject userInfo = data.getJSONObject("userInfo");
            if (userInfo == null) {
                return null;
            }

            JSONObject user = userInfo.getJSONObject("user");
            authorInfo.setAuthorId(StringUtil.parseStr(user, "id"));
            authorInfo.setUniqueId(StringUtil.parseStr(user, "uniqueId"));
            authorInfo.setAuthorName(StringUtil.parseStr(user, "nickname"));
            authorInfo.setAuthorAvatar(StringUtil.parseStr(user, "avatarThumb"));
            authorInfo.setSecUid(StringUtil.parseStr(user, "secUid"));
            authorInfo.setAuthorUrl("https://www.tiktok.com/@" + StringUtil.parseStr(user, "uniqueId"));
            authorInfo.setDesc(StringUtil.parseStr(user, "signature"));
            authorInfo.setRegisterTime(StringUtil.parseInt(user, "createTime"));
            authorInfo.setIsVerified(StringUtil.booleanToInt(StringUtil.parseBoolean(user, "verified")));

            authorInfo.setRegion(StringUtil.parseStr(user, "region"));
            authorInfo.setLanguage(StringUtil.parseStr(user, "language"));
            authorInfo.setPrivateAccount(StringUtil.booleanToInt(StringUtil.parseBoolean(user, "privateAccount")));

            try {
                JSONObject commerceUserInfo = user.getJSONObject("commerceUserInfo");
                Boolean isCommerceUser = StringUtil.parseBoolean(commerceUserInfo, "commerceUser");
                authorInfo.setCommerceUser(StringUtil.booleanToInt(isCommerceUser));

                if (isCommerceUser) {
                    authorInfo.setCategory(StringUtil.parseStr(commerceUserInfo, "category"));
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            authorInfo.setTtSeller(StringUtil.parseInt(user, "ttSeller"));

            JSONObject stats = data.getJSONObject("stats");
            if (stats != null) {
                authorInfo.setFollowerCount(StringUtil.parseInt(stats, "followerCount"));
                authorInfo.setFollowingCount(StringUtil.parseInt(stats, "followingCount"));
                authorInfo.setHeartCount(StringUtil.parseInt(stats, "heartCount"));
                authorInfo.setVideoCount(StringUtil.parseInt(stats, "videoCount"));
                authorInfo.setFriendCount(StringUtil.parseInt(stats, "friendCount"));
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

        return authorInfo;
    }

    /**
     * 通过网关调用服务
     *
     * @param url
     * @return
     */
    public JSONObject callGet(String url) {
        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
        headers.setAccept(Collections.singletonList(type));
        headers.setBearerAuth("6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==");
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36");

        try {
            // 创建 HttpEntity 对象，包含请求头和请求体（如果需要）
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送 GET 请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            String body = response.getBody();
            log.info("TikHubXhsClient response={}", response);

            JSONObject object = JSON.parseObject(body);
            Integer code = object.getInteger("code");
            if (!code.equals(200)) {
                log.error("TikHubXhsClient 查询{}服务接口出错:", url);
                return null;
            }

            return object;
        } catch (Exception e) {
            log.error("TikHubXhsClient 调用AutoPaas服务接口出错:", e);
        }

        return null;
    }


    /**
     * 通过网关调用服务
     *
     * @param url
     * @param reqJson
     * @return
     */
    public void callPostNoResponse(String url, String reqJson) {
        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());

        HttpEntity formEntity = new HttpEntity(reqJson, headers);

        try {
            String response = restTemplate.postForEntity(url, formEntity, String.class).getBody();

            log.info("http response={}", response);
        } catch (Exception e) {
            log.error("http 调用AutoPaas服务接口出错:", e);
        }

    }

}
