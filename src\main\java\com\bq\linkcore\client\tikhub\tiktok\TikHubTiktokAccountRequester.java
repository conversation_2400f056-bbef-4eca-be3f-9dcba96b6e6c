package com.bq.linkcore.client.tikhub.tiktok;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bq.linkcore.client.tikhub.models.AuthorInfo;
import com.bq.linkcore.client.tikhub.models.TikHubArticle;
import com.bq.linkcore.client.tikhub.models.TikHubArticleMusic;
import com.bq.linkcore.client.tikhub.models.TikHubArticleVideo;
import com.bq.linkcore.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/29 16:32
 * @className TikHubDouYinClient
 * @description
 */

@Slf4j
@Component
public class TikHubTiktokAccountRequester {
    @Resource(name = "commonRestTemplate")
    private RestTemplate restTemplate;

    public AuthorInfo getUserProfileWebV1(String uniqueId, String secUid) {
        String url = "https://api.tikhub.io/api/v1/tiktok/web/fetch_user_profile?";
        if (StringUtils.isNotBlank(uniqueId)) {
            url += "uniqueId=" + uniqueId;
        } else {
            if (StringUtils.isNotBlank(secUid)) {
                url += "secUid=" + secUid;
            } else {
                return null;
            }
        }

        JSONObject object = callGet(url);
        if (object == null) {
            return null;
        }

        AuthorInfo authorInfo = new AuthorInfo();
        try {
            JSONObject data = object.getJSONObject("data");
            Integer code = data.getInteger("statusCode");
            if (code != 0) {
                return null;
            }

            JSONObject userInfo = data.getJSONObject("userInfo");
            if (userInfo == null) {
                return null;
            }

            JSONObject user = userInfo.getJSONObject("user");
            authorInfo.setAuthorId(StringUtil.parseStr(user, "id"));
            authorInfo.setUniqueId(StringUtil.parseStr(user, "uniqueId"));
            authorInfo.setAuthorName(StringUtil.parseStr(user, "nickname"));
            authorInfo.setAuthorAvatar(StringUtil.parseStr(user, "avatarThumb"));
            authorInfo.setSecUid(StringUtil.parseStr(user, "secUid"));
            authorInfo.setAuthorUrl("https://www.tiktok.com/@" + StringUtil.parseStr(user, "uniqueId"));
            authorInfo.setDesc(StringUtil.parseStr(user, "signature"));
            authorInfo.setRegisterTime(StringUtil.parseInt(user, "createTime"));
            authorInfo.setIsVerified(StringUtil.booleanToInt(StringUtil.parseBoolean(user, "verified")));

            authorInfo.setRegion(StringUtil.parseStr(user, "region"));
            authorInfo.setLanguage(StringUtil.parseStr(user, "language"));
            authorInfo.setPrivateAccount(StringUtil.booleanToInt(StringUtil.parseBoolean(user, "privateAccount")));

            try {
                JSONObject commerceUserInfo = user.getJSONObject("commerceUserInfo");
                Boolean isCommerceUser = StringUtil.parseBoolean(commerceUserInfo, "commerceUser");
                authorInfo.setCommerceUser(StringUtil.booleanToInt(isCommerceUser));

                if (isCommerceUser) {
                    authorInfo.setCategory(StringUtil.parseStr(commerceUserInfo, "category"));
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

            authorInfo.setTtSeller(StringUtil.parseInt(user, "ttSeller"));

            JSONObject stats = data.getJSONObject("stats");
            if (stats != null) {
                authorInfo.setFollowerCount(StringUtil.parseInt(stats, "followerCount"));
                authorInfo.setFollowingCount(StringUtil.parseInt(stats, "followingCount"));
                authorInfo.setHeartCount(StringUtil.parseInt(stats, "heartCount"));
                authorInfo.setVideoCount(StringUtil.parseInt(stats, "videoCount"));
                authorInfo.setFriendCount(StringUtil.parseInt(stats, "friendCount"));
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

        return authorInfo;
    }

    public List<TikHubArticle> getUserArticlesByWebV1(String uniqueId, String secUid, Integer count) {
        List<TikHubArticle> tikHubArticleList = new ArrayList<>();

        Long cursor = 0L;
        Integer pageSize = 20;
        Integer coverFormat = 2;
        Integer sort = 0;
        Boolean hasMore = true;

        while (hasMore) {
            JSONObject object = getUserArticlesPageByWebV1(secUid, cursor, pageSize, coverFormat, sort);
            JSONObject data = object.getJSONObject("data");
            cursor = StringUtil.strToLong(data.getString("cursor"));
            hasMore = data.getBoolean("hasMore");

            JSONArray itemList = object.getJSONArray("itemList");
            for (int index = 0; index < itemList.size(); index++) {
                try {
                    JSONObject item = itemList.getJSONObject(index);

                    TikHubArticle tikHubArticle = new TikHubArticle();
                    JSONObject author = item.getJSONObject("author");
                    tikHubArticle.setAuthorId(StringUtil.parseStr(author, "id"));
                    tikHubArticle.setUniqueId(StringUtil.parseStr(author, "uniqueId"));
                    tikHubArticle.setSecUid(StringUtil.parseStr(author, "secUid"));

                    tikHubArticle.setWorkId(StringUtil.parseStr(item, "id"));
                    tikHubArticle.setWorkUuid(StringUtil.parseStr(item, "id"));
                    String workUrl = "https://www.tiktok.com/@" + StringUtil.parseStr(author, "uniqueId") + "/video/" + tikHubArticle.getWorkId();
                    tikHubArticle.setUrl(workUrl);

                    tikHubArticle.setCategoryType(StringUtil.parseInt(item, "CategoryType"));
                    tikHubArticle.setIsAd(StringUtil.booleanToInt(StringUtil.parseBoolean(item, "isAd")));
                    tikHubArticle.setTitle(StringUtil.parseStr(item, "desc"));
                    tikHubArticle.setContent(StringUtil.parseStr(item, "desc"));
                    tikHubArticle.setHashTags(parseHashTag(item));

                    tikHubArticle.setPublishTime(StringUtil.parseInt(item, "createTime"));
                    tikHubArticle.setTextLanguage(StringUtil.parseStr(item, "textLanguage"));

                    JSONObject stats = item.getJSONObject("stats");
                    tikHubArticle.setPlayCount(StringUtil.parseInt(stats, "playCount"));
                    tikHubArticle.setCollectCount(StringUtil.parseInt(stats, "collectCount"));
                    tikHubArticle.setCommentCount(StringUtil.parseInt(stats, "commentCount"));
                    tikHubArticle.setLikeCount(StringUtil.parseInt(stats, "diggCount"));
                    tikHubArticle.setShareCount(StringUtil.parseInt(stats, "shareCount"));

                    TikHubArticleVideo tikHubArticleVideo = parseVideoWebV1(item);
                    tikHubArticle.setVideo(tikHubArticleVideo);
                    tikHubArticle.setMusic(parseMusicWebV1(item));
                    tikHubArticle.setThumbnailLink(tikHubArticleVideo.getCover());

                    tikHubArticleList.add(tikHubArticle);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }

            if (itemList.size() == 0 || tikHubArticleList.size() >= count) {
                break;
            }
        }

        return tikHubArticleList;
    }

    private TikHubArticleVideo parseVideoWebV1(JSONObject note) {
        TikHubArticleVideo tikHubArticleVideo = new TikHubArticleVideo();

        JSONObject video = note.getJSONObject("video");
        tikHubArticleVideo.setVideoID(StringUtil.parseStr(video, "videoID"));
        tikHubArticleVideo.setVideoQuality(StringUtil.parseStr(video, "videoQuality"));
        tikHubArticleVideo.setVqscore(StringUtil.parseStr(video, "VQScore"));
        tikHubArticleVideo.setBitrate(StringUtil.parseLong(video, "bitrate"));
        tikHubArticleVideo.setCodecType(StringUtil.parseStr(video, "codecType"));
        tikHubArticleVideo.setDefinition(StringUtil.parseStr(video, "definition"));
        tikHubArticleVideo.setDuration(StringUtil.parseLong(video, "duration"));
        tikHubArticleVideo.setDataSize(StringUtil.parseLong(video, "dataSize"));
        tikHubArticleVideo.setHeight(StringUtil.parseInt(video, "height"));
        tikHubArticleVideo.setWidth(StringUtil.parseInt(video, "width"));
        tikHubArticleVideo.setCover(StringUtil.parseStr(video, "cover"));

        return tikHubArticleVideo;
    }

    private TikHubArticleMusic parseMusicWebV1(JSONObject note) {
        TikHubArticleMusic articleMusic = new TikHubArticleMusic();
        JSONObject music = note.getJSONObject("music");

        articleMusic.setMusicId(StringUtil.parseStr(music, "id"));
        articleMusic.setTitle(StringUtil.parseStr(music, "title"));
        articleMusic.setAuthorName(StringUtil.parseStr(music, "authorName"));
        articleMusic.setAuthorAvatar(StringUtil.parseStr(music, "coverThumb"));
        articleMusic.setDuration(StringUtil.parseInt(music, "duration"));
        articleMusic.setIsCopyrighted(StringUtil.booleanToInt(StringUtil.parseBoolean(music, "isCopyrighted")));
        articleMusic.setOriginal(StringUtil.booleanToInt(StringUtil.parseBoolean(music, "original")));
        articleMusic.setUrl(StringUtil.parseStr(music, "playUrl"));

        return articleMusic;
    }

    private List<String> parseHashTag(JSONObject note) {
        List<String> hashTags = new ArrayList<>();
        try {
            JSONArray textExtraList = note.getJSONArray("textExtra");
            for (int index = 0; index < textExtraList.size(); index++) {
                JSONObject extra = textExtraList.getJSONObject(index);
                hashTags.add(extra.getString("hashtagName"));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return hashTags;
    }


    public JSONObject getUserArticlesPageByWebV1(String secUid,
                                                 Long cursor,
                                                 Integer pageSize,
                                                 Integer coverFormat,
                                                 Integer sort) {
        JSONObject object = getUserArticlesPageInnerByWebV1(secUid, cursor, pageSize, coverFormat, sort);
        if (object == null) {
            object = getUserArticlesPageInnerByWebV1(secUid, cursor, pageSize, coverFormat, sort);
        }

        if (object == null) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            object = getUserArticlesPageInnerByWebV1(secUid, cursor, pageSize, coverFormat, sort);
        }

        return object;
    }


    public JSONObject getUserArticlesPageInnerByWebV1(String secUid,
                                                      Long cursor,
                                                      Integer pageSize,
                                                      Integer coverFormat,
                                                      Integer sort) {
        String url = "https://api.tikhub.io/api/v1/tiktok/web/fetch_user_post?";
        url += "secUid=" + secUid;
        url += "&cursor=" + cursor;
        url += "&count=" + pageSize;
        url += "&coverFormat=" + coverFormat;
        url += "&post_item_list_request_type=" + sort;

        return callGet(url);
    }


    /**
     * 通过网关调用服务
     *
     * @param url
     * @return
     */
    public JSONObject callGet(String url) {
        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
        headers.setAccept(Collections.singletonList(type));
        headers.setBearerAuth("6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==");
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36");

        try {
            // 创建 HttpEntity 对象，包含请求头和请求体（如果需要）
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送 GET 请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            String body = response.getBody();
            log.info("TikHubXhsClient response={}", response);

            JSONObject object = JSON.parseObject(body);
            Integer code = object.getInteger("code");
            if (!code.equals(200)) {
                log.error("TikHubXhsClient 查询{}服务接口出错:", url);
                return null;
            }

            return object;
        } catch (Exception e) {
            log.error("TikHubXhsClient 调用AutoPaas服务接口出错:", e);
        }

        return null;
    }


    /**
     * 通过网关调用服务
     *
     * @param url
     * @param reqJson
     * @return
     */
    public void callPostNoResponse(String url, String reqJson) {
        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());

        HttpEntity formEntity = new HttpEntity(reqJson, headers);

        try {
            String response = restTemplate.postForEntity(url, formEntity, String.class).getBody();

            log.info("http response={}", response);
        } catch (Exception e) {
            log.error("http 调用AutoPaas服务接口出错:", e);
        }

    }

}
