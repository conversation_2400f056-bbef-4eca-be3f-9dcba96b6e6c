package com.bq.linkcore.services.impl.monitor.provider;

import com.bq.linkcore.services.impl.monitor.external.DouyinApiService;
import com.bq.linkcore.services.impl.monitor.storage.DouyinDataStorageService;
import com.bq.linkcore.services.pool.ThreadRejectPolicy;
import com.bq.linkcore.services.pool.WorkThreadFactory;
import com.bq.linkcore.services.pool.WorkThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 抖音监控提供者
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
public class DouyinMonitoringProvider {

    private final static String DOUYIN_THREAD_TAG = "douyin-provider";

    /**
     * 抖音专用线程池
     */
    private final WorkThreadPool douyinThreadPool = new WorkThreadPool(
            3,
            8,
            2L,
            TimeUnit.MINUTES,
            new LinkedBlockingQueue<>(5000),
            new WorkThreadFactory(DOUYIN_THREAD_TAG),
            new ThreadRejectPolicy(DOUYIN_THREAD_TAG)
    );

    @Resource
    private DouyinApiService douyinApiService;

    @Resource
    private DouyinDataStorageService douyinDataStorageService;

    /**
     * 处理用户列表 - 综合处理
     */
    public void processUsers(List<String> uniqueIds) {
        log.info("抖音提供者开始处理用户，数量: {}", uniqueIds.size());
        
        for (String uniqueId : uniqueIds) {
            douyinThreadPool.addTask(() -> {
                try {
                    processUserInternal(uniqueId);
                } catch (Exception e) {
                    log.error("抖音提供者处理用户异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }

    /**
     * 处理用户作者信息
     */
    public void processAuthorProfiles(List<String> uniqueIds) {
        log.info("抖音提供者开始处理作者信息，数量: {}", uniqueIds.size());
        
        for (String uniqueId : uniqueIds) {
            douyinThreadPool.addTask(() -> {
                try {
                    processAuthorProfileInternal(uniqueId);
                } catch (Exception e) {
                    log.error("抖音提供者处理作者信息异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }

    /**
     * 处理用户作品
     */
    public void processUserWorks(List<String> uniqueIds) {
        log.info("抖音提供者开始处理用户作品，数量: {}", uniqueIds.size());
        
        for (String uniqueId : uniqueIds) {
            douyinThreadPool.addTask(() -> {
                try {
                    processUserWorksInternal(uniqueId);
                } catch (Exception e) {
                    log.error("抖音提供者处理用户作品异常，uniqueId: {}", uniqueId, e);
                }
            });
        }
    }

    /**
     * 内部用户处理逻辑
     */
    private void processUserInternal(String uniqueId) {
        log.debug("开始处理抖音用户: {}", uniqueId);
        
        // 1. 获取用户基本信息
        var userProfile = douyinApiService.getUserProfile(uniqueId);
        if (userProfile != null) {
            douyinDataStorageService.saveUserProfile(userProfile);
        }
        
        // 2. 获取用户作品
        var userWorks = douyinApiService.getUserWorks(uniqueId);
        if (userWorks != null && !userWorks.isEmpty()) {
            douyinDataStorageService.saveUserWorks(userWorks);
        }
        
        log.debug("完成处理抖音用户: {}", uniqueId);
    }

    /**
     * 内部作者信息处理逻辑
     */
    private void processAuthorProfileInternal(String uniqueId) {
        log.debug("开始处理抖音作者信息: {}", uniqueId);
        
        var userProfile = douyinApiService.getUserProfile(uniqueId);
        if (userProfile != null) {
            douyinDataStorageService.saveUserProfile(userProfile);
            log.debug("成功保存抖音作者信息: {}", uniqueId);
        } else {
            log.warn("未获取到抖音作者信息: {}", uniqueId);
        }
    }

    /**
     * 内部用户作品处理逻辑
     */
    private void processUserWorksInternal(String uniqueId) {
        log.debug("开始处理抖音用户作品: {}", uniqueId);
        
        var userWorks = douyinApiService.getUserWorks(uniqueId);
        if (userWorks != null && !userWorks.isEmpty()) {
            douyinDataStorageService.saveUserWorks(userWorks);
            log.debug("成功保存抖音用户作品: {}, 数量: {}", uniqueId, userWorks.size());
        } else {
            log.warn("未获取到抖音用户作品: {}", uniqueId);
        }
    }

    /**
     * 批量处理用户
     */
    public void batchProcessUsers(List<String> uniqueIds, int batchSize) {
        log.info("抖音提供者开始批量处理用户，总数量: {}, 批次大小: {}", uniqueIds.size(), batchSize);
        
        for (int i = 0; i < uniqueIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, uniqueIds.size());
            List<String> batch = uniqueIds.subList(i, endIndex);
            
            douyinThreadPool.addTask(() -> {
                try {
                    processBatchInternal(batch);
                } catch (Exception e) {
                    log.error("抖音提供者批量处理异常，批次: {}-{}", i, endIndex, e);
                }
            });
        }
    }

    /**
     * 内部批次处理逻辑
     */
    private void processBatchInternal(List<String> batch) {
        log.debug("开始处理抖音批次，数量: {}", batch.size());
        
        for (String uniqueId : batch) {
            try {
                processUserInternal(uniqueId);
                
                // 添加延迟避免频繁请求
                Thread.sleep(500);
                
            } catch (Exception e) {
                log.error("批次处理单个用户异常，uniqueId: {}", uniqueId, e);
            }
        }
        
        log.debug("完成处理抖音批次，数量: {}", batch.size());
    }

    /**
     * 获取提供者状态
     */
    public String getProviderStatus() {
        return String.format("抖音提供者[任务数: %d, 线程数: %d]", 
                douyinThreadPool.getTaskCount(), douyinThreadPool.getThreadCount());
    }

    /**
     * 停止提供者
     */
    public void shutdown() {
        log.info("开始停止抖音监控提供者");
        douyinThreadPool.stop();
        log.info("抖音监控提供者已停止");
    }
}
