package com.bq.linkcore.client.tikhub;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bq.linkcore.client.tikhub.models.ArticleDetailModel;
import com.bq.linkcore.utils.DateTimeUtil;
import com.bq.linkcore.utils.StringUtil;
import com.bq.linkcore.utils.TikTokVideoIdExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/29 16:32
 * @className TikHubDouYinClient
 * @description
 */

@Slf4j
@Component
public class TikHubTiktokClient {
    @Resource(name = "commonRestTemplate")
    private RestTemplate restTemplate;

    public ArticleDetailModel getAppV3TiktokDetailV1(String workUrl) {
        String workId = TikTokVideoIdExtractor.getTikTokVideoId(workUrl);
        if (StringUtils.isBlank(workId)) {
            return null;
        }

        String url = "https://api.tikhub.io/api/v1/tiktok/app/v3/fetch_one_video?aweme_id=";
        url += workId;

        JSONObject object = callGet(url);
        if (object == null) {
            return null;
        }

        ArticleDetailModel articleDetailModel = new ArticleDetailModel();
        try {
            JSONObject data = object.getJSONObject("data");
            Integer code = data.getInteger("status_code");
            if (code != 0) {
                return null;
            }

            JSONArray d = data.getJSONArray("aweme_details");
            if (d.size() == 0) {
                return null;
            }

            JSONObject note = d.getJSONObject(0);

            JSONObject user = note.getJSONObject("author");
            String author_id = user.getString("unique_id");
            articleDetailModel.setAuthorId(author_id);
            articleDetailModel.setAuthorIdentity(StringUtil.parseStr(user, "sec_uid"));
            articleDetailModel.setAuthorName(StringUtil.parseStr(user, "nickname"));
            articleDetailModel.setAuthorUrl("https://www.tiktok.com/@" + author_id);
            JSONObject avatarThumb = user.getJSONObject("avatar_thumb");
            try {
                String avatarUrl = (String) avatarThumb.getJSONArray("url_list").get(0);
                articleDetailModel.setAuthorAvatar(avatarUrl);
            } catch (Exception e) {
            }

            articleDetailModel.setWorkId(workId);
            articleDetailModel.setWorkUuid(workId);
            articleDetailModel.setUrl(StringUtil.parseStr(note, "share_url"));
            articleDetailModel.setTitle(StringUtil.parseStr(note, "desc"));
            articleDetailModel.setContent(StringUtil.parseStr(note, "desc"));
            articleDetailModel.setDigest(StringUtil.parseStr(note, "desc"));

            Long ts = note.getLong("create_time");
            articleDetailModel.setPublishTime(DateTimeUtil.covertDateTime(ts));
            // articleDetailModel.setLocationIp(StringUtil.parseStr(note, "ip_location"));

            JSONObject statistics = note.getJSONObject("statistics");
            articleDetailModel.setCollectCount(StringUtil.parseInt(statistics, "collect_count"));
            articleDetailModel.setLikeCount(StringUtil.parseInt(statistics, "digg_count"));
            articleDetailModel.setCommentCount(StringUtil.parseInt(statistics, "comment_count"));
            articleDetailModel.setShareCount(StringUtil.parseInt(statistics, "share_count"));
            articleDetailModel.setReadCount(StringUtil.parseInt(statistics, "play_count"));

            try {
                JSONObject video = note.getJSONObject("video");
                JSONObject cover = video.getJSONObject("cover");
                String thumbnailLink = (String) cover.getJSONArray("url_list").get(0);
                articleDetailModel.setThumbnailLink(thumbnailLink);

                JSONObject play = video.getJSONObject("play_addr");
                JSONArray playUrlList = play.getJSONArray("url_list");
                for (int index = 0; index < playUrlList.size(); index++) {
                    String playUrl = playUrlList.getString(index);
                    if (playUrl.contains("aweme/v1/play")) {
                        articleDetailModel.setVideoUrls(playUrl);
                        break;
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }

        return articleDetailModel;
    }

    public List<ArticleDetailModel> getAppV3TiktokUserVideosV1(String secUserId,
                                                               String userUniqueId,
                                                               Integer sortType,
                                                               Integer count) {
        Integer maxCursor = 0;
        Integer hasMore = 1;
        List<ArticleDetailModel> articleDetailModelList = new ArrayList<>();

        while (hasMore == 1) {
            JSONObject object = getAppV3TiktokUserVideosV1Inner(secUserId, userUniqueId, maxCursor, sortType, count);
            if (object == null) {
                break;
            }

            try {
                JSONObject data = object.getJSONObject("data");
                maxCursor = data.getInteger("max_cursor");
                hasMore = data.getInteger("has_more");

                JSONArray notes = object.getJSONArray("aweme_list");
                for (int index = 0; index < notes.size(); index++) {
                    JSONObject note = notes.getJSONObject(index);

                    ArticleDetailModel articleDetailModel = new ArticleDetailModel();

                    String workId = StringUtil.parseStr(note, "aweme_id");
                    JSONObject user = note.getJSONObject("author");
                    String author_id = user.getString("unique_id");
                    articleDetailModel.setAuthorId(author_id);
                    articleDetailModel.setAuthorIdentity(StringUtil.parseStr(user, "sec_uid"));
                    articleDetailModel.setAuthorName(StringUtil.parseStr(user, "nickname"));
                    articleDetailModel.setAuthorUrl("https://www.tiktok.com/@" + author_id);
                    JSONObject avatarThumb = user.getJSONObject("avatar_thumb");
                    try {
                        String avatarUrl = (String) avatarThumb.getJSONArray("url_list").get(0);
                        articleDetailModel.setAuthorAvatar(avatarUrl);
                    } catch (Exception e) {
                    }

                    articleDetailModel.setWorkId(workId);
                    articleDetailModel.setWorkUuid(workId);
                    articleDetailModel.setUrl(StringUtil.parseStr(note, "share_url"));
                    articleDetailModel.setTitle(StringUtil.parseStr(note, "desc"));
                    articleDetailModel.setContent(StringUtil.parseStr(note, "desc"));
                    articleDetailModel.setDigest(StringUtil.parseStr(note, "desc"));

                    Long ts = note.getLong("create_time");
                    articleDetailModel.setPublishTime(DateTimeUtil.covertDateTime(ts));
                    // articleDetailModel.setLocationIp(StringUtil.parseStr(note, "ip_location"));

                    JSONObject statistics = note.getJSONObject("statistics");
                    articleDetailModel.setCollectCount(StringUtil.parseInt(statistics, "collect_count"));
                    articleDetailModel.setLikeCount(StringUtil.parseInt(statistics, "digg_count"));
                    articleDetailModel.setCommentCount(StringUtil.parseInt(statistics, "comment_count"));
                    articleDetailModel.setShareCount(StringUtil.parseInt(statistics, "share_count"));
                    articleDetailModel.setReadCount(StringUtil.parseInt(statistics, "play_count"));

                    try {
                        JSONObject video = note.getJSONObject("video");
                        JSONObject cover = video.getJSONObject("cover");
                        String thumbnailLink = (String) cover.getJSONArray("url_list").get(0);
                        articleDetailModel.setThumbnailLink(thumbnailLink);

                        JSONObject play = video.getJSONObject("play_addr");
                        JSONArray playUrlList = play.getJSONArray("url_list");
                        for (int j = 0; j < playUrlList.size(); j++) {
                            String playUrl = playUrlList.getString(j);
                            if (playUrl.contains("aweme/v1/play")) {
                                articleDetailModel.setVideoUrls(playUrl);
                                break;
                            }
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }

                    articleDetailModelList.add(articleDetailModel);
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                break;
            }

            if (articleDetailModelList.size() >= count) {
                break;
            }
        }

        return articleDetailModelList;
    }

    private JSONObject getAppV3TiktokUserVideosV1Inner(String secUserId,
                                                       String userUniqueId,
                                                       Integer maxCursor,
                                                       Integer sortType,
                                                       Integer pageSize) {
        String url = "https://api.tikhub.io/api/v1/tiktok/app/v3/fetch_user_post_videos?";
        url += "max_cursor=" + maxCursor;
        url += "&count=" + pageSize;
        url += "&sort_type=" + sortType;
        url += "&sec_user_id=" + secUserId;
        url += "&unique_id=" + userUniqueId;

        JSONObject object = callGet(url);
        if (object == null) {
            object = callGet(url);
        }

        if (object == null) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            object = callGet(url);
        }

        return object;
    }

    /**
     * 通过网关调用服务
     *
     * @param url
     * @return
     */
    public JSONObject callGet(String url) {
        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json;charset=UTF-8");
        headers.setAccept(Collections.singletonList(type));
        headers.setBearerAuth("6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==");
        headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36");

        try {
            // 创建 HttpEntity 对象，包含请求头和请求体（如果需要）
            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送 GET 请求
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    entity,
                    String.class
            );

            String body = response.getBody();
            log.info("TikHubXhsClient response={}", response);

            JSONObject object = JSON.parseObject(body);
            Integer code = object.getInteger("code");
            if (!code.equals(200)) {
                log.error("TikHubXhsClient 查询{}服务接口出错:", url);
                return null;
            }

            return object;
        } catch (Exception e) {
            log.error("TikHubXhsClient 调用AutoPaas服务接口出错:", e);
        }

        return null;
    }


    /**
     * 通过网关调用服务
     *
     * @param url
     * @param reqJson
     * @return
     */
    public void callPostNoResponse(String url, String reqJson) {
        HttpHeaders headers = new HttpHeaders();

        MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
        headers.setContentType(type);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());

        HttpEntity formEntity = new HttpEntity(reqJson, headers);

        try {
            String response = restTemplate.postForEntity(url, formEntity, String.class).getBody();

            log.info("http response={}", response);
        } catch (Exception e) {
            log.error("http 调用AutoPaas服务接口出错:", e);
        }

    }

}
